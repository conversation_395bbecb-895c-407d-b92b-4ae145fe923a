import java.text.SimpleDateFormat
import java.util.Date

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.kotlin.ksp)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildApp.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")

android {
    namespace = "com.flutterup.app"

    defaultConfig {
        applicationId = "com.flutterup.app"
        minSdk = 26
        targetSdk = 36
        versionCode = SimpleDateFormat("yyyyMMdd").format(Date().time).toInt()
        versionName = "1.0.0"

        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation(project(":libs:base"))
    implementation(project(":libs:network"))
    implementation(project(":libs:tracking"))
    implementation(project(":libs:billinghelper"))
    implementation(project(":libs:gifts"))
    implementation(project(":libs:players"))

    implementation(libs.androidx.splashscreen)
    implementation(libs.googleid)
    implementation(libs.google.credentials)
    implementation(libs.google.auth)


    //模糊工具
    implementation(libs.haze)
    implementation(libs.haze.materials)

    //cropper
    implementation(libs.cropper)

    //db
    implementation(libs.androidx.room)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    implementation(libs.compose.shimmer)
}