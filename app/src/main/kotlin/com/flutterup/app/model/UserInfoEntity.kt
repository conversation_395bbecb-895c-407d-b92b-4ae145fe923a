package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.flutterup.app.R
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

const val GENDER_UNSPECIFIED: Int = 0

const val GENDER_MALE: Int = 1

const val GENDER_FEMALE: Int = 2

const val GENDER_NON_BINARY: Int = 3

/**
 * 用户信息接口
 */
@Keep
interface IUserInfo {
    val userId: String?
    val token: String?
    val imToken: String?
    val nickname: String?
    val headImage: String?
    val isNewUser: Int?
    val isModel: Boolean?
    val sign: String?
    val sex: Int?
    val sexuality: Int?
    val age: Int?
    val isHide: Int?
    val birthday: Long?
    val mediaList: List<MediaItemEntity>?
    val tags: List<String>?
    val online: Int?
    val right: Right?
    val scene: Int?
    val userFlag: Int?
    val pushConfig: Int?
    val location: Int?
    val refer: Int?

    /**
     * 权限信息接口
     */
    @Keep
    interface Right {
        val vip: Int?
        val flashChat: Int?
        val privacyVideo: Int?
        val privacyImage: Int?
        val likeCount: Int?
        val swipeCount: Int?
    }
}


@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class UserInfo(
    @Json(name = "user_id") override val userId: String? = null,

    @Json(name = "auth_token") override val token: String? = null,

    @Json(name = "im_token") override val imToken: String? = null,

    @Json(name = "nick_name") override val nickname: String? = null,

    @Json(name = "headimg") override val headImage: String? = null,

    @Json(name = "is_new_user") override val isNewUser: Int? = null,

    @Json(name = "is_model") override val isModel: Boolean? = null,

    @Json(name = "sign") override val sign: String? = null,

    /**
     * 性别 1男 2女 3未知
     */
    @Json(name = "sex") override val sex: Int? = null,


    /**
     * 性取向 1男 2女 3其他
     */
    @Json(name = "sexuality") override val sexuality: Int? = null,

    @Json(name = "age") override val age: Int? = null,

    @Json(name = "is_hide") override val isHide: Int? = null,

    @Json(name = "brithday") override val birthday: Long? = null,

    @Json(name = "media_list") override val mediaList: List<MediaItemEntity>? = null,

    @Json(name = "tags") override val tags: List<String>? = null,

    @Json(name = "online") override val online: Int? = null, // Consider Int/Boolean/Enum

    @Json(name = "right") override val right: UserRightsEntity? = null,

    /**
     * 1 正常用户
     * 2 正常用户
     * 3 fb审核人员
     */
    @Json(name = "scene") override val scene: Int? = null,

    /**
     * 用户标记位，用于标记用户的一些特殊状态，该字段目前是一个二进制位图，每一位代表一种状态
     *
     * 例如:
     *
     * 00000001 表示用户开启了位置共享
     *
     * 00000010 表示用户放弃了账号
     *
     * 00000100 表示用户开启了通知
     *
     * 00001000 表示用户开启了隐身
     *
     * 00010000 表示用户已经提示过app评分
     *
     * 00100000 表示用户无法正常支付
     *
     * 01000000 表示用户当前在审核版本
     *
     * 10000000 表示用户注册时在审核版本
     */
    @Json(name = "user_flag") override val userFlag: Int? = null,

    /**
     *
     */
    @Json(name = "push_config") override val pushConfig: Int? = null,

    @Json(name = "location") override val location: Int? = null,

    /**
     * 用户和当前用户的关系, 一般是获取他人信息的时候才会有这个字段
     *
     * - 0: 已经建联
     * - 1: 我已喜欢对方
     * - 2: 对方已喜欢我
     * - 3: 没有任何关系
     */
    @Json(name = "refer") override val refer: Int? = null,
) : Parcelable, IUserInfo {

    val isInvalid: Boolean
        get() = isNewUser == 1

    val pingRefer: PingRefer? get() = PingRefer.fromRefer(refer)

    /**
     * 判断是否是审核模式
     */
    fun isAly(): Boolean {
        return scene == 3
    }

}


enum class Gender(val value: Int, val stringResource: Int) {

    UNSPECIFIED(GENDER_UNSPECIFIED, stringResource = R.string.unspecified),
    MALE(GENDER_MALE, stringResource = R.string.male),
    FEMALE(GENDER_FEMALE, stringResource = R.string.female),
    NON_BINARY(GENDER_NON_BINARY, stringResource = R.string.non_binary);

    fun toTrackingValue(): Int {
        return when(this) {
            UNSPECIFIED, MALE -> 0
            FEMALE -> 1
            NON_BINARY -> 2
        }
    }

    companion object {
        val options = listOf(MALE, FEMALE, NON_BINARY)

        fun fromValue(value: Int?): Gender {
            return entries.firstOrNull { it.value == value } ?: UNSPECIFIED
        }
    }
}

enum class PingRefer {
    PINGED, //已经建联

    WINK_SENT, //发送了wink

    WINKED, //被wink了

    NONE; //无

    companion object {

        fun fromRefer(refer: Int?): PingRefer? = when(refer) {
            0 -> PINGED
            1 -> WINK_SENT
            2 -> WINKED
            3 -> NONE
            else -> null
        }
    }
}
