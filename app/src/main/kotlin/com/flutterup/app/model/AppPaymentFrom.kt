package com.flutterup.app.model

// 支付来源类型，使用枚举替代原先的整型常量，便于类型安全和可读性
enum class AppPaymentFrom(val value: Int) {

    MATCH_LIMIT(1), // 来自match次数购买vip
    EXPLORE_LIKE_LIMIT(2), // 来自首页的like限制购买vip
    PROFILE_LIKE_LIMIT(3), // 来自profile的like限制购买vip
    WLM_LIKE_LIMIT(4), // 来自wlm的like限制购买vip
    CUPID_LIKE_LIMIT(5), // 来自cupidchat的like限制购买vip
    FC_NO_VIP_EXPLORE_LIMIT(6), // 来自首页的非vip下flashchat限制购买vip
    FC_NO_VIP_U_LIKE_LIMIT(7), // 来自likes的youliked下非vip购买vip
    FC_VIP_U_LIKE_LIMIT(8), // 来自likes的youliked下vip购买flashchat
    ICE_CHAT_LIMIT(9), // 来自破冰的聊天限制购买vip
    EFFECT_CHAT_LIMIT(10), // 来自有效会话的聊天限制购买vip
    DEEP_CHAT_LIMIT(11), // 来自深度回话的聊天限制购买vip
    PHOTO_NO_VIP_LIMIT(12), // 来自聊天的私密图片非vip下购买vip
    VIDEO_NO_VIP_LIMIT(13), // 来自聊天的私密视频非vip购买vip
    PHOTO_VIP_LIMIT(14), // 来自聊天的私密图片vip下购买私密图片
    VIDEO_VIP_LIMIT(15), // 来自聊天的私密视频vip下购买私密视频
    CHAT_ALBUM_LIMIT(16), // 来自聊天界面的私密相册解锁vip
    PROFILE_ALBUM_LIMIT(17), // 来自个人主页的私密相册解锁vip
    PROFILE_NO_VIP_FC(18), // 来自个人主页非会员情况下通过flashchat解锁vip
    PROFILE_NO_VIP_PHOTO(19), // 来自个人主页非会员情况下通过photo解锁vip
    PROFILE_NO_VIP_VIDEO(20), // 来自个人主页非会员情况下通过video解锁vip
    PROFILE_VIP_FC(21), // 来自个人主页会员情况下购买flashchat
    PROFILE_VIP_PHOTO(22), // 来自个人主页会员情况下购买photo
    PROFILE_VIP_VIDEO(23), // 来自个人主页会员情况下购买video
    PROFILE_VIP(24), // 来自个人主页会员入口vip情况下购买vip
    SYS_WLM(25), // 来自系统推送的wlm解锁vip
    SYS_VISITOR(26), // 来自系统推送的cupidchat解锁vip
    PROFILE_VIP_HEAD(27), // 来自profile的头像挂件会员下点击vip
    OTHER_PROFILE_NO_VIP_FC(28), // 来自个人主页非会员情况下通过flashchat解锁vip
    OTHER_PROFILE_VIP_FC(29), // 来自个人主页会员情况下购买flashchat
    MAX_CHAT_LIMIT(30), // 来自100条的聊天限制购买vip
    HIDE(31), // 来自解锁隐身的购买vip
    PROFILE_NO_VIP(32), // 来自个人主页会员入口非vip情况下购买vip
    PROFILE_NO_VIP_HEAD(33), // 来自profile的头像挂件非会员下解锁vip
    SEND_PRIVACY_LIMIT(34), // 发送私密消息触发需要解锁vip
    PRIVACY_LIST_LIMIT(35), // 在私密列表触发了购买vip
    FC_VIP_EXPLORE_LIMIT(36), // 来自首页的vip下flashchat限制购买flashchat
    PRIVATE_NUM_LIMIT(37), // 收到私密内容数量限制解锁vip
    SESSION_PRIVATE_LIMIT(38), // 来自点击列表会话私密内容数量限制下购买vip
    IM_LOCK_NOTICE_LIMIT(39), // 来自点击消息推送私密内容数量限制下购买vip
    MSG_MATCH_CARD_LIMIT(40), // 来自点击新建联消息卡片时购买vip
    LIKES_TAB_WLM(41), // 来自likes的wlm底部入口购买vip
    VISITOR_PAGE(42), // 来自访客底部入口购买vip
    VISITOR_ITEM(43), // 来自访客Item点击购买vip
    REPEATING(44), // 来自小号重复注册购买vip
    EXPIRING_SOON(45), // 来自快到期提醒续费购买vip
    EXPIRED(46), // 来自已到期提醒 购买vip
    MULTIPLE_PRIVACY_PHOTO(47), // 解锁打包私密图片
    MULTIPLE_PRIVACY_VIDEO(48), // 解锁打包私密视频
    MULTIPLE_PHOTO_NO_VIP_LIMIT(49), // 解锁打包私密图片非vip下购买vip
    MULTIPLE_VIDEO_NO_VIP_LIMIT(50), // 解锁打包私密视频非vip购买vip
    SYS_FIX(51), // 系统补单
    ADMIN_FIX(52), // 后台补单
    EXPLORE_PROFILE(53), // 首页浏览查看资料
    UNKNOWN(99); // 未知

    companion object {
        // 根据整型值反向查找枚举，不存在时返回未知
        fun fromValue(value: Int): AppPaymentFrom {
            return entries.firstOrNull { it.value == value } ?: UNKNOWN
        }
    }
}
