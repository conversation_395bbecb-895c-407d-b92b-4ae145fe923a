package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize


@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class UserRightsEntity(
    @<PERSON><PERSON>(name = "vip") override val vip: Int? = null,
    @<PERSON><PERSON>(name = "flashchat") override val flashChat: Int? = null,
    @<PERSON><PERSON>(name = "privacyvideo") override val privacyVideo: Int? = null,
    @<PERSON><PERSON>(name = "privacyimage") override val privacyImage: Int? = null,
    @<PERSON><PERSON>(name = "like_cnt") override val likeCount: Int? = null,
    @<PERSON><PERSON>(name = "swipe_cnt") override val swipeCount: Int? = null,
) : Parcelable, IUserInfo.Right


data class UserRightsState(
    val rights: UserRightsEntity?
) {
    val isVip: Boolean
        get() = rights?.vip == 1

    val swipeCount: Int
        get() = rights?.swipeCount ?: 0

    val isSwipeEmpty: Boolean
        get() = !isVip && swipeCount <= 0
}