package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * 用户各种数据的数量
 */
@Keep
@JsonClass(generateAdapter = true)
data class UserCountEntity(
    @Json(name = "visitor_new_num") val visitorNewNum: Int = 0, // Consider Int
    @Json(name = "visitor_total_num") val visitorTotalNum: Int = 0, // Consider Int
    @Json(name = "wlm_new_num") val wlmNewNum: Int = 0, // Consider Int
    @Json(name = "wlm_total_num") val wlmTotalNum: Int = 0, // Consider Int
    @Json(name = "head_list") val headList: List<String> = emptyList(), // Avatars for WLM?
    @Json(name = "likes_avatar") val likesAvatar: String? = null, // Avatar for I Like?
    @Json(name = "visitor_avatar") val visitorAvatar: String? = null // Avatar for Visitors?
) {

    companion object {

        @JvmField
        val EMPTY = UserCountEntity()
    }
}