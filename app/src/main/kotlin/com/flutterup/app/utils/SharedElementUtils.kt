package com.flutterup.app.utils

object SharedElementUtils {

    const val KEY_NICKNAME = "nickname"

    fun generatorProfileMediaKey(userId: String?, mediaIndex: Int): SharedElementKey {
        return ProfileMediaSharedElementKey(userId.orEmpty(), mediaIndex)
    }

    fun generatorProfileIconKey(userId: String?): SharedElementKey {
        return ProfileIconSharedElementKey(userId.orEmpty())
    }
}

sealed interface SharedElementKey

sealed class ProfileSharedElementKey(
    open val userId: String,
    val type: ProfileSharedElementType
) : SharedElementKey

enum class ProfileSharedElementType {
    Media,

    Icon
}

data class ProfileMediaSharedElementKey(
    override val userId: String,
    val mediaIndex: Int
) : ProfileSharedElementKey(userId, ProfileSharedElementType.Media)

data class ProfileIconSharedElementKey(
    override val userId: String
) : ProfileSharedElement<PERSON><PERSON>(userId, ProfileSharedElementType.Icon)
