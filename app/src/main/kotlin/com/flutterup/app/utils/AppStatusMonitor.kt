package com.flutterup.app.utils

import com.flutterup.app.network.ApiService
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AppStatusMonitorImpl @Inject constructor(
    private val apiService: ApiService,
) : AppStatusMonitor {

    @Inject
    lateinit var userRepository: UserRepository

    override suspend fun onAppStatusChanged(status: AppStatus): Boolean {
        if (!userRepository.isLogin) return false

        try {
            val reportResult = apiService.reportAppStatus(status.value)
            return reportResult.isSuccess
        } catch (_: Exception) {
            return false
        }
    }
}


interface AppStatusMonitor {
    suspend fun onAppStatusChanged(status: AppStatus): Boolean
}

enum class AppStatus(val value: Int) {
    BACKGROUND(0),

    FOREGROUND(1),

    LOGOUT(4),
}