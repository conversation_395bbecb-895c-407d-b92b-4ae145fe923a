@file:OptIn(ExperimentalSharedTransitionApi::class)

package com.flutterup.app.navigation

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomNavigation
import androidx.compose.material.BottomNavigationItem
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.compose.NavHost
import com.flutterup.app.screen.AppState
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.MainActivityUIState
import com.flutterup.app.screen.chat.chatGraph
import com.flutterup.app.screen.common.webViewScreen
import com.flutterup.app.screen.developer.developerGraph
import com.flutterup.app.screen.homeBottomNavGraph
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.loginGraph
import com.flutterup.app.screen.payment.paymentGraph
import com.flutterup.app.screen.profile.profileGraph
import com.flutterup.app.screen.settings.settingsGraph
import kotlin.reflect.KClass

@Composable
fun AppScreen(
    uiState: MainActivityUIState,
    modifier: Modifier = Modifier,
) {
    val appState = LocalAppState.current


    Scaffold(
        bottomBar = {
            if (appState.currentDestination.isRouteInBottomNav()) {
                BottomNavigation(
                    modifier = Modifier
                        .shadow(
                            elevation = 12.dp,
                            shape = RoundedCornerShape(
                                topStart = 24.dp,
                                topEnd = 24.dp,
                                bottomStart = 0.dp,
                                bottomEnd = 0.dp
                            )
                        )
                        .height(WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding() + 56.dp)
                    ,
                    backgroundColor = Color.White,
                ) {
                    appState.topLevelDestinations.forEach { destination ->
                        val selected = appState.currentDestination.isRouteInHierarchy(destination.routeType)

                        BottomNavigationItem(
                            selected = selected,
                            onClick = { appState.navigateToTopLevelDestination(destination) },
                            icon = {
                                val icon = if (selected) destination.selectedIcon else destination.unselectedIcon
                                BadgedBox(
                                    badge = { BottomNavigationBadge(appState, destination) }
                                ) {
                                    Icon(
                                        painter = painterResource(icon),
                                        contentDescription = null,
                                        tint = Color.Unspecified
                                    )
                                }
                            },
                            alwaysShowLabel = false
                        )
                    }
                }
            }
        },
        modifier = modifier
    ) { innerPadding ->
        CompositionLocalProvider(
            LocalInnerPadding provides innerPadding,
        ) {
            FlutterUpNavHost(
                appState = appState,
                uiState = uiState,
            )
        }
    }
}

@Composable
fun FlutterUpNavHost(
    appState: AppState,
    uiState: MainActivityUIState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController

    if (uiState.shouldKeepSplashScreen()) {
        return
    }

    val startDestination = when (uiState) {
        is MainActivityUIState.Success -> HomeBaseRoute::class
        else -> LoginBaseRoute::class
    }


    SharedTransitionLayout {
        NavHost(
            navController = navController,
            startDestination = startDestination,
            modifier = modifier,
            enterTransition = { NavigationAnimations.Presets.horizontalSlide.enter },
            exitTransition = { NavigationAnimations.Presets.horizontalSlide.exit },
            popEnterTransition = { NavigationAnimations.Presets.horizontalSlide.popEnter },
            popExitTransition = { NavigationAnimations.Presets.horizontalSlide.popExit }
        ) {
            loginGraph(uiState)

            homeBottomNavGraph(
                sharedTransitionScope = this@SharedTransitionLayout
            )

            webViewScreen()

            developerGraph()

            profileGraph(
                sharedTransitionScope = this@SharedTransitionLayout
            )

            chatGraph()

            settingsGraph()

            paymentGraph()
        }
    }
}

@Composable
private fun BottomNavigationBadge(
    appState: AppState,
    topLevelDestination: TopLevelDestination
) {
    when(topLevelDestination) {
        TopLevelDestination.RELATE -> {
            val state = appState.relateUnreadMessageCount.collectAsStateWithLifecycle()
            val userCountEntity by remember { state }
            val count = userCountEntity.wlmNewNum + userCountEntity.visitorNewNum
            BadgeContent(count)
        }
        TopLevelDestination.CHAT -> {
            //TODO 消息未读数据
        }
        else -> {
        }
    }
}

@Composable
private fun BadgeContent(count: Int) {
    if (count <= 0) return
    val text = if (count > 99) "+99" else count.toString()
    Badge(
        containerColor = Color.Red,
        contentColor = Color.White
    ) {
        Text(text)
    }
}


private fun NavDestination?.isRouteInHierarchy(route: KClass<*>) =
    this?.hierarchy?.any {
        it.hasRoute(route)
    } ?: false

private fun NavDestination?.isRouteInBottomNav() =
    TopLevelDestination.entries.any { destination ->
        this?.hierarchy?.any { it.hasRoute(destination.routeType) } ?: false
    }
