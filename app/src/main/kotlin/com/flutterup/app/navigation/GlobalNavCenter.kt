package com.flutterup.app.navigation

import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.navOptions
import com.flutterup.app.utils.UserRepository
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.LoginProfileStep1Route
import com.flutterup.app.screen.login.LoginProfileStep2Route
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局导航中心
 * 用于管理全局导航相关逻辑
 */
@Singleton
class GlobalNavCenter @Inject constructor(
    private val userRepository: UserRepository
) {
    private var navController: NavHostController? = null

    fun setNavController(navController: NavHostController) {
        this.navController = navController
    }

    fun navigateLogin() {
        navController?.navigate(LoginBaseRoute)
    }

    /**
     * 导航到首页, 会检查是是无效用户
     */
    fun navigateHome() {
        if (userRepository.isInvalid) { //是无效用户, 跳转资料填写页
            navigateToLoginProfileStep1()
            return
        }

        navController?.navigate(HomeBaseRoute)
    }

    fun navigateToLoginProfileStep1() {
        navController?.navigate(LoginProfileStep1Route)
    }

    fun navigateToLoginProfileStep2() {
        navController?.navigate(LoginProfileStep2Route)
    }


    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        navController?.let {
            val topLevelNavOptions = navOptions {
                // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                popUpTo(it.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免重新选择同一项目时产生多个相同目标的副本
                launchSingleTop = true
                // 重新选择之前选择的项目时恢复状态
                restoreState = true
            }

            it.navigate(topLevelDestination.route, topLevelNavOptions)
        }
    }

    fun navigateToTopLevelDestination(route: BottomNavRoute) {
        navController?.let {
            val topLevelNavOptions = navOptions {
                // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                popUpTo(it.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免重新选择同一项目时产生多个相同目标的副本
                launchSingleTop = true
                // 重新选择之前选择的项目时恢复状态
                restoreState = true
            }

            it.navigate(route, topLevelNavOptions)
        }
    }
}