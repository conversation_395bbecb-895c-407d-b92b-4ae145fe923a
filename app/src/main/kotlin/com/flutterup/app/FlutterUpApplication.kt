package com.flutterup.app

import com.flutterup.app.utils.GlobalCoroutineExceptionHandler
import com.flutterup.base.BaseApplication
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineExceptionHandler

@HiltAndroidApp
class FlutterUpApplication : BaseApplication() {

    override fun exceptionHandler(): CoroutineExceptionHandler {
        return GlobalCoroutineExceptionHandler
    }
}