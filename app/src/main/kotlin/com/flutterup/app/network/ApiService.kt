package com.flutterup.app.network

import com.flutterup.app.model.ChangePasswordEntity
import com.flutterup.app.model.DiscoverListEntity
import com.flutterup.app.model.UpdateProfileRequest
import com.flutterup.app.model.UpdateProfileResult
import com.flutterup.app.model.UserActionEntity
import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.model.VisitorEntity
import com.flutterup.app.model.WinksEntity
import com.flutterup.network.BaseResponse
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ApiService {
    /**
     * Sign in with Email
     */
    @FormUrlEncoded
    @POST("/signin/email")
    suspend fun signInWithEmail(
        @Field("email") email: String,
        @Field("pwd") password: String
    ) : BaseResponse<UserInfo>


    /**
     * 编辑个人资料
     * @param request
     * @see [com.flutterup.app.model.UpdateProfileRequest]
     */
    @POST("/user/editmineprofile")
    suspend fun updateProfileInfo(@Body request: UpdateProfileRequest) : BaseResponse<UpdateProfileResult>


    /**
     * 获取用户各种列表的数量数据
     */
    @POST("/user/listdetail")
    suspend fun getUserListNum(): BaseResponse<UserCountEntity>

    /**
     * 获取滑卡列表
     */
    @FormUrlEncoded
    @POST("/swipe/list")
    suspend fun getSwipeUserList(
        @Field("last_ids") lastId: String? = null,
        @Field("size") size: Int = 20,
        @Field("tab") tab: Int? = null
    ) : BaseResponse<DiscoverListEntity>


    /**
     * 获取其他用户的个人资料
     */
    @FormUrlEncoded
    @POST("/user/otherinfo")
    suspend fun getAnotherProfileInfo(@Field("user_id") userId: String): BaseResponse<UserInfo>

    /**
     * 对用户进行选择操作
     * @param type 1: dislike, 2: like
     * @param cardFlag 0: default, 1: connected card
     */
    @FormUrlEncoded
    @POST("/user/operate")
    suspend fun userAction(
        @Field("user_id") userId: String,
        @Field("type") type: Int,
        @Field("from") from: Int? = null,
        @Field("card_flag") cardFlag: Int = 0
    ) : BaseResponse<UserActionEntity>


    /**
     * 获取用户喜欢/被喜欢列表
     * @param tag 1: WLM, 3: I Like
     */
    @FormUrlEncoded
    @POST("/user/likelist")
    suspend fun getWinksList(
        @Field("tag") tag: Int,
        @Field("last_id") lastId: Long? = null,
    ): BaseResponse<WinksEntity>

    /**
     * 已读某种类型的喜欢列表
     */
    @FormUrlEncoded
    @POST("/user/readlike")
    suspend fun markAllWinksRead(@Field("from") from: Int): BaseResponse<Any>


    /**
     * 获取访客列表
     */
    @FormUrlEncoded
    @POST("/user/visitors")
    suspend fun getVisitorList(@Field("last_id") lastId: Long?): BaseResponse<VisitorEntity>


    /**
     * 重设密码
     */
    @POST("/user/changepwd")
    suspend fun changePassword(@Body body: ChangePasswordEntity) : BaseResponse<Any>

    /**
     * 删除账号
     */
    @POST("/user/remove")
    suspend fun deleteAccount() : BaseResponse<Any>


    /**
     * app状态上报
     */
    @FormUrlEncoded
    @POST("/user/activestatus")
    suspend fun reportAppStatus(@Field("status") status: Int): BaseResponse<Any>


    /**
     * 更新通知设置
     */
    @FormUrlEncoded
    @POST("/user/upgradenotify")
    suspend fun updateNotificationSettings(@Field("config") config: Int): BaseResponse<Any>
}