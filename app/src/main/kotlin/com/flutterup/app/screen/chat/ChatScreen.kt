package com.flutterup.app.screen.chat

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.relate.RelateRoute
import com.flutterup.app.screen.relate.state.Visitors

@Composable
fun ChatScreen(
    modifier: Modifier = Modifier,
) {
    val innerPadding = LocalInnerPadding.current
    val appState = LocalAppState.current

    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(bottom = innerPadding.calculateBottomPadding())
            .clickable(onClick = {
                appState.navigateToTopLevelDestination(RelateRoute(index = Visitors.EMPTY.index))
            })
    ) {
        Text(text = "Chat", modifier = Modifier.align(Alignment.Center), color = TextBlack)
    }
}