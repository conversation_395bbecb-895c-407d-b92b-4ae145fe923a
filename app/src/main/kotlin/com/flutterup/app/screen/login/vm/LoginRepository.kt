package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepository
import com.flutterup.network.BaseResponse
import com.flutterup.network.ktx.safeApiCall
import javax.inject.Inject

class LoginRepository @Inject constructor(
    private val apiService: ApiService,
    private val userRepository: UserRepository
) : BaseRepository() {

    suspend fun login(email: String, password: String): BaseResponse<UserInfo> {
        val result = apiService.signInWithEmail(email, password)
        if (result.isSuccess) {
            userRepository.updateUserInfo(result.data) // 更新用户信息
        }
        return result
    }

}