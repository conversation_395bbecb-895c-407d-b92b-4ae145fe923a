package com.flutterup.app.screen.settings.state

import com.flutterup.app.R
import com.flutterup.app.model.UserNotificationEntity
import com.flutterup.base.BaseState

data class NotificationState(
    val pushConfig: Int? = null,
    override val isLoading: Boolean = false
) : BaseState {

    val types = NotificationType.entries.toList()

    enum class NotificationType(val titleRes: Int, val mask: Int) {
        Message(R.string.notification_title_new_message, UserNotificationEntity.SETTINGS_NEW_MSG),

        Match(R.string.notification_title_new_match, UserNotificationEntity.SETTINGS_NEW_MATCH),

        Winks(R.string.notification_title_new_winks, UserNotificationEntity.SETTINGS_NEW_WINKS),

        Visitor(R.string.notification_title_new_visitor, UserNotificationEntity.SETTINGS_NEW_VISITOR);

        fun checked(pushConfig: Int?): Boolean = pushConfig != null && pushConfig.hasFlag(mask)
    }
}

private fun Int.hasFlag(mask: Int): <PERSON><PERSON><PERSON> {
    return this and mask != 0
}