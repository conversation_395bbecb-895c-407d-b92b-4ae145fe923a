package com.flutterup.app.screen.relate.state

import com.flutterup.app.R
import kotlinx.serialization.Serializable

data class RelateHomeState(
    val titles: List<RelateTitle>,

    val initialPage: Int = 0,
) {
    constructor() : this(listOf(WinksReceived.EMPTY, WinksSent.EMPTY, Visitors.EMPTY))
}

sealed class RelateTitle(
    open val titleRes: Int,
    open val count: Int,

    open val index: Int,
) {
    val isAboveZero: Boolean
        get() = count > 0
}

data class WinksReceived(override val count: Int = 0) : RelateTitle(R.string.winks_received, count, 0) {

    companion object {
        val EMPTY = WinksReceived(0)
    }
}

data class WinksSent(override val count: Int = 0) : RelateTitle(R.string.winks_sent, count, 1) {
    companion object {
        val EMPTY = WinksSent(0)
    }
}

data class Visitors(override val count: Int = 0) : RelateTitle(R.string.visitor, count, 2) {
    companion object {
        val EMPTY = Visitors(0)
    }
}