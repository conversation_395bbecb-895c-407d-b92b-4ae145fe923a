package com.flutterup.app.screen.profile.state

import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.PingRefer

data class OtherProfileUiState(
    val userId: String,

    val headImg: String? = null,
    val online: Boolean = false,
    val nickname: String? = null,
    val age: Int? = null,
    val fullMediaList: List<MediaItemEntity> = emptyList(),
    val mediaList: List<String> = fullMediaList.map { it.safelyUrl },
    val intro: String? = null,
    val interests: List<String>? = null,
    val refer: PingRefer? = null,
) {

    companion object {
        val EMPTY = OtherProfileUiState(
            "",
        )
    }
}