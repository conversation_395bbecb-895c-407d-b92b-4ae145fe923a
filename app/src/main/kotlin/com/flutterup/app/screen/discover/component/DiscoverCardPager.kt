package com.flutterup.app.screen.discover.component

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.Margin
import com.flutterup.app.design.component.cardstack.CardStack
import com.flutterup.app.design.component.cardstack.CardStackState
import com.flutterup.app.design.component.cardstack.Direction
import com.flutterup.app.design.component.cardstack.SwipeDirection
import com.flutterup.app.design.component.pager.LinearPagerIndicator
import com.flutterup.app.design.component.pager.LinearPagerIndicatorDefaults
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.PinkPrimary
import com.flutterup.app.design.theme.PurpleGradientPrimary
import com.flutterup.app.design.theme.ShimmerOnPrimary
import com.flutterup.app.design.theme.ShimmerPrimary
import com.flutterup.app.model.DiscoverItemEntity
import com.flutterup.app.screen.discover.state.DiscoverState
import com.flutterup.app.utils.SharedElementUtils
import com.valentinilk.shimmer.Shimmer
import com.valentinilk.shimmer.shimmer


@ExperimentalSharedTransitionApi
@Composable
fun ColumnScope.DiscoverCardPager(
    uiState: DiscoverState,
    cardStackState: CardStackState,
    shimmerOnPrimary: Shimmer,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    topCardCurrentIndex: Int,
    onDrag: (direction: Direction, progress: Float) -> Unit,
    onSwiped: (direction: Direction, index: Int) -> Unit,
    onProfileClick: (DiscoverItemEntity, Int) -> Unit,
) {
    CardStack(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp)
            .weight(1f),
        cardStackState = cardStackState,
        enable = !uiState.isAllLoaded,
        stackDirection = Direction.Left,
        swipeDirection = SwipeDirection.Horizontal,
        cardElevation = 20.dp,
        scaleRatio = 0.9f,
        visibleCount = 3,
        displacementThreshold = 120.dp,
        rotationMaxDegree = 15,
        items = uiState.discoverList,
        onDrag = onDrag,
        onSwiped = onSwiped
    ) { index, item ->
        DiscoverCardItem(
            uiState = uiState,
            shimmerOnPrimary = shimmerOnPrimary,
            index = index,
            item = item,
            sharedTransitionScope = sharedTransitionScope,
            animatedContentScope = animatedContentScope,
            topCardCurrentIndex = topCardCurrentIndex,
            modifier = Modifier.fillMaxHeight(),
            onProfileClick = onProfileClick
        )
    }
}

@ExperimentalSharedTransitionApi
@Composable
private fun DiscoverCardItem(
    uiState: DiscoverState,
    shimmerOnPrimary: Shimmer,
    index: Int,
    item: DiscoverItemEntity,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    topCardCurrentIndex: Int,
    modifier: Modifier = Modifier,
    onProfileClick: (DiscoverItemEntity, Int) -> Unit
) {
    val pagerState = rememberPagerState(initialPage = 0) { item.mediaList.size }
    val shape = RoundedCornerShape(26.dp)

    if (index == 0) { //只有顶部的卡片会响应
        LaunchedEffect(topCardCurrentIndex) {
            pagerState.scrollToPage(topCardCurrentIndex)
        }
    }

    val gradientModifier = if (index == 0 && !uiState.isAllLoaded) Modifier.background(
        brush = Brush.verticalGradient(
            colors = listOf(
                PurpleGradientPrimary.copy(0f),
                PurpleGradientPrimary.copy(0.6f),
            )
        )
    ) else Modifier

    val coverModifier = if (index != 0 && !uiState.isAllLoaded)
        Modifier.background(PinkPrimary.copy(0.8f))
    else Modifier

    Card(
        modifier = modifier
            .noRippleClickable(
                onClick = {
                    onProfileClick(item, pagerState.currentPage)
                },
                enabled = index == 0
            ),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = ShimmerPrimary,
            contentColor = ShimmerPrimary,
        ),
        border = if (index == 0) BorderStroke(1.dp, Color.White) else null
    ) {
        Box(Modifier.fillMaxSize()) {
            VerticalPager(pagerState, Modifier.fillMaxSize()) { pagerIndex ->
                val media = item.mediaList.getOrNull(pagerIndex) ?: return@VerticalPager

                with(sharedTransitionScope) {
                    AsyncImage(
                        model = media.safelyUrl,
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .fillMaxSize()
                            .sharedElement(
                                sharedContentState = sharedTransitionScope.rememberSharedContentState(
                                    key = SharedElementUtils.generatorProfileMediaKey(
                                        item.userId,
                                        pagerIndex
                                    )
                                ),
                                animatedVisibilityScope = animatedContentScope
                            )
                            .clip(shape)
                    )
                }
            }

            //indicator
            LinearPagerIndicator(
                pageCount = item.mediaList.size,
                selectedPage = pagerState.currentPage,
                config = LinearPagerIndicatorDefaults.copy(
                    orientation = Orientation.Vertical,
                    shape = RoundedCornerShape(10.dp),
                ),
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 20.dp, end = 10.dp)
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomStart)
                    .then(gradientModifier)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 20.dp, end = 20.dp, bottom = 18.dp)
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(7.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        ShimmerContent(
                            uiState = uiState,
                            shimmer = shimmerOnPrimary,
                            modifier = Modifier
                                .size(width = 109.dp, height = 20.dp)
                                .clip(RoundedCornerShape(13.dp))
                        ) {
                            Text(
                                text = item.nickname.orEmpty() + " " + item.age.toString(),
                                style = TextStyle(
                                    fontSize = 24.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.W900,
                                    color = Color.White,
                                )
                            )
                        }

                        ShimmerContent(
                            uiState = uiState,
                            shimmer = shimmerOnPrimary,
                            modifier = Modifier
                                .size(8.dp)
                                .clip(CircleShape)
                        ) {
                            if (item.online == 1) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_online_dot),
                                    contentDescription = null,
                                    tint = Color.Unspecified,
                                    modifier = Modifier.size(8.dp)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(6.dp))

                    ShimmerContent(
                        uiState = uiState,
                        shimmer = shimmerOnPrimary,
                        shimmerContent = {
                            Column(verticalArrangement = Arrangement.spacedBy(10.dp)) {
                                Row(horizontalArrangement = Arrangement.spacedBy(3.dp)) {
                                    Box(
                                        modifier = Modifier
                                            .size(15.dp)
                                            .clip(CircleShape)
                                            .shimmer(shimmerOnPrimary)
                                            .background(ShimmerOnPrimary)
                                    )

                                    Box(
                                        modifier = Modifier
                                            .size(width = 45.dp, height = 15.dp)
                                            .clip(RoundedCornerShape(13.dp))
                                            .shimmer(shimmerOnPrimary)
                                            .background(ShimmerOnPrimary)
                                    )
                                }

                                Row(horizontalArrangement = Arrangement.spacedBy(10.dp)) {
                                    Box(
                                        modifier = Modifier
                                            .size(width = 61.dp, height = 16.dp)
                                            .clip(RoundedCornerShape(13.dp))
                                            .shimmer(shimmerOnPrimary)
                                            .background(ShimmerOnPrimary)
                                    )

                                    Box(
                                        modifier = Modifier
                                            .size(width = 63.dp, height = 16.dp)
                                            .clip(RoundedCornerShape(13.dp))
                                            .shimmer(shimmerOnPrimary)
                                            .background(ShimmerOnPrimary)
                                    )
                                }
                            }
                        }
                    ) {
                        item.interests?.let { interests ->
                            Row(horizontalArrangement = Arrangement.spacedBy(7.dp)) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_interest_logo),
                                    contentDescription = null,
                                    tint = Color.Unspecified,
                                    modifier = Modifier.size(12.dp)
                                )

                                Text(
                                    text = stringResource(R.string.interest_uppercase),
                                    style = TextStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight.W400,
                                        color = Color.White,
                                    )
                                )
                            }

                            Spacer(modifier = Modifier.height(10.dp))

                            FlowRow(
                                horizontalArrangement = Arrangement.spacedBy(10.dp),
                                verticalArrangement = Arrangement.spacedBy(10.dp),
                                maxLines = 1, //只有单行
                                modifier = Modifier.padding(end = 68.dp)
                            ) {
                                repeat(interests.size) { index ->
                                    val interest = interests.getOrNull(index) ?: return@repeat

                                    Box(
                                        modifier = Modifier
                                            .padding(horizontal = 5.dp, vertical = 2.dp)
                                            .background(
                                                color = Color.White.copy(alpha = 0.2f),
                                                shape = RoundedCornerShape(size = 8.dp)
                                            )
                                    ) {
                                        Text(
                                            text = interest,
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                lineHeight = 12.sp,
                                                fontWeight = FontWeight.W400,
                                                color = Color.White,
                                            )
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                Margin(
                    paddingValues = PaddingValues(end = 18.dp, bottom = 18.dp),
                    modifier = Modifier.align(Alignment.BottomEnd)
                ) {
                    ShimmerContent(
                        uiState = uiState,
                        shimmer = shimmerOnPrimary,
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                    ) {
                        with(sharedTransitionScope) {
                            Icon(
                                painter = painterResource(R.drawable.ic_discover_explore),
                                contentDescription = null,
                                tint = Color.Unspecified,
                                modifier = Modifier.sharedElement(
                                    sharedContentState = sharedTransitionScope.rememberSharedContentState(
                                        SharedElementUtils.generatorProfileIconKey(item.userId)
                                    ),
                                    animatedVisibilityScope = animatedContentScope,
                                )
                            )
                        }
                    }
                }
            }

            if (item.type != null && item.type != 0) {
                Icon(
                    painter = painterResource(R.drawable.ic_discover_meetagain),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(bottom = 44.dp)
                )
            }

            Spacer(Modifier.fillMaxSize().then(coverModifier))
        }
    }
}