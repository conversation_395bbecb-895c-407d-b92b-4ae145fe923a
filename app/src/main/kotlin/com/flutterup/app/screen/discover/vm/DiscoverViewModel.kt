package com.flutterup.app.screen.discover.vm

import com.flutterup.app.design.component.cardstack.Direction
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.DiscoverItemEntity
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.model.UserActionType
import com.flutterup.app.model.UserRightsEntity
import com.flutterup.app.model.UserRightsState
import com.flutterup.app.screen.discover.state.DiscoverState
import com.flutterup.app.screen.profile.vm.UserActionRepository
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.utils.Timber
import com.google.android.gms.common.api.ApiException
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class DiscoverViewModel @Inject constructor(
    private val repository: DiscoverRepository,
    private val actionRepository: UserActionRepository,
    private val userRepository: UserRepository
) : BaseRepositoryViewModel(repository, actionRepository) {

    private val _uiState = MutableStateFlow(DiscoverState.EMPTY)

    val uiState: StateFlow<DiscoverState> = combine(
        _uiState,
        loadingState,
        userRepository.userInfoState
    ) { ui, loading, userInfo ->
        ui.copy(
            isLoading = loading,
            headimg = userInfo?.headImage,
            nickname = userInfo?.nickname
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    val rights: StateFlow<UserRightsState> = userRepository.userInfoState.map { userInfo ->
        UserRightsState(userInfo?.right)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        UserRightsState(userRepository.userInfo?.right)
    )


    init {
        getDiscoverList()
    }

    fun getDiscoverList() {
        scope.launchWithLoading {
            val result = repository.getDiscoverList()
            if (result != null) {
                result.rights?.let { rights ->
                    userRepository.updateUserRight(rights)
                }

                mergedDiscoverList(result.matchList)
            }
        }
    }

    fun onSwiped(direction: Direction) {
        val data = _uiState.value.data
        val first = data.matchList.firstOrNull() ?: return

        val userId = first.userId ?: return
        val type = if (direction == Direction.Right) UserActionType.Like else UserActionType.Dislike
        val cardType = if (first.type != 0) UserActionCardType.Connected else UserActionCardType.Default

        dropCard(first)
        scope.launch {
            try {
                val isSuccess = actionRepository.userAction(
                    userId = userId,
                    type = type,
                    from = AppFrom.Discover,
                    cardFlag = cardType
                )

                if (!isSuccess) {
                    restoreCard(first)
                }
            } catch (e: Exception) { //报错了
                restoreCard(first)
                throw e //重新抛出，交给全局异常处理去解决
            }
        }.invokeOnCompletion {

        }
    }

    /**
     * 合并新的数据到旧的数据中
     */
    private fun mergedDiscoverList(newList: List<DiscoverItemEntity>) {
        if (newList.isEmpty()) return //不需要合并

        val oldList = _uiState.value.data.matchList
        val mergedList = (oldList + newList).distinctBy { it.userId }.filterNot { it.userId == null }
        _uiState.update { it.copy(data = it.data.copy(matchList = mergedList)) }
    }

    private fun dropCard(card: DiscoverItemEntity) {
        val list = _uiState.value.data.matchList
        val newList = list.filterNot { it.userId == card.userId }
        _uiState.update { it.copy(data = it.data.copy(matchList = newList)) }
    }

    private fun restoreCard(card: DiscoverItemEntity) {
        val list = _uiState.value.data.matchList
        val newList = list.toMutableList().also { it.add(0, card) }
        _uiState.update { it.copy(data = it.data.copy(matchList = newList)) }
    }
}