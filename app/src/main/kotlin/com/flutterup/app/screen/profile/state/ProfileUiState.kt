package com.flutterup.app.screen.profile.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.flutterup.app.R
import kotlin.enums.EnumEntries

data class ProfileUiState(
    val nickname: String? = null,
    val headimg: String? = null,

    val newVisitorsCount: Int = 0,
    val newVisitorsHeadimg: List<String> = emptyList(),

    //权益相关的
    val isVip: Boolean = false,
    val pp: Int = 0,
    val pv: Int = 0,
    val pingchat: Int = 0,
    val diamond: Double = 0.00,
    val isAly: Boolean = false, //是否是审核模式
) {

    /**
     * 权益列表
     */
    val packs: List<ProfilePacks> get() = if (isAly) {
        listOf(ProfilePacksPingChat(pingchat))
    } else {
        listOf(
            ProfilePacksPhoto(pp),
            ProfilePacksVideo(pv),
            ProfilePacksPingChat(pingchat),
        )
    }

    val options: EnumEntries<ProfileHomeOptions> = ProfileHomeOptions.entries
}

sealed class ProfilePacks(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
)

data class ProfilePacksPhoto(
    val count: Int,
) : ProfilePacks(R.string.private_photos, R.mipmap.ic_packs_picture)

data class ProfilePacksVideo(
    val count: Int,
) : ProfilePacks(R.string.private_videos, R.mipmap.ic_packs_video)

data class ProfilePacksPingChat(
    val count: Int,
) : ProfilePacks(R.string.ping_chat, R.mipmap.ic_packs_pingchat)

enum class ProfileHomeOptions(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
) {
    Settings(R.string.setting, R.drawable.ic_profile_home_settings),

    HelpCenter(R.string.help_center, R.drawable.ic_profile_home_helpcenter),

    PrivatePolicy(R.string.privacy_policy, R.drawable.ic_profile_home_privacy_policy),

    TermsPolicy(R.string.terms_policy, R.drawable.ic_profile_home_termservice),
}


sealed class ProfileOptions(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
    val isShowRightArrow: Boolean = true
)