package com.flutterup.app.screen.common

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.screen.LocalNavController
import kotlinx.serialization.Serializable

@Serializable data class WebViewRoute(
    val title: String?,
    val url: String,
)

fun NavGraphBuilder.webViewScreen() {
    composable<WebViewRoute> { backStackEntry ->
        val webViewRoute = backStackEntry.toRoute<WebViewRoute>()
        CustomWebViewScreen(webViewRoute.title, webViewRoute.url)
    }
}


@Composable
fun CustomWebViewScreen(
    title: String?,
    url: String,
) {
    val navController = LocalNavController.current

    ScaffoldWebViewScreen(
        title = title,
        url = url,
        onBackClick = {
            navController.popBackStack()
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ScaffoldWebViewScreen(
    title: String?,
    url: String,
    onBackClick: () -> Unit,
) {
    var webTitle by remember { mutableStateOf(title) }

    AppScaffold (
        onBackClick = onBackClick,
        title = {
            // 如果传了title就显示，否则显示网页标题，都没有则为空
            if (!title.isNullOrEmpty()) {
                AppTitleText(title)
            } else {
                webTitle?.let { AppTitleText(it) }
            }
        }
    ) { paddingValues ->
        CustomWebView(
            url = url,
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            onTitleChange = { newTitle ->
                // 只有在没有传入标题时才更新网页标题
                if (title.isNullOrEmpty()) {
                    webTitle = newTitle
                }
            },
            onBackPressed = onBackClick
        )
    }
}

@Preview
@Composable
private fun CustomWebViewScreenPreview() {
    ScaffoldWebViewScreen(
        title = "WebView",
        url = "https://www.google.com",
        onBackClick = {}
    )
}
