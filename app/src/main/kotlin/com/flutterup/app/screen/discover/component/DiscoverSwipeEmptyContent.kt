package com.flutterup.app.screen.discover.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.Margin
import com.flutterup.app.design.theme.AppTheme
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState

data class CardTransform(
    val offsetX: Dp,
    val offsetY: Dp,
    val rotation: Float,
    val zIndex: Float,
    val width: Dp,
    val height: Dp,
    val brush: Brush? = null
)

@Composable
fun DiscoverSwipeEmptyContent(
    items: List<String>?,
    modifier: Modifier = Modifier,
    transforms: List<CardTransform> = defaultCardTransform,
) {
    Column(
        modifier = modifier.wrapContentSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(Modifier.height(30.dp))

        TransformCards(
            items = items,
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp)
                .offset(y = 35.dp),
            transforms = transforms
        )


        AsyncImage(
            model = R.drawable.ic_smiling_face,
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.End)
                .padding(end = 82.dp)
                .offset(y = 20.dp)
        )

        Margin(
            paddingValues = PaddingValues(start = 23.dp, end = 35.dp, bottom = 24.dp)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_great_matches),
                contentDescription = null,
                modifier = Modifier
                    .weight(1f)
                    .height(98.dp)
            )
        }


        Margin(
            paddingValues = PaddingValues(start = 62.dp, end = 73.dp, bottom = 47.dp)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_get_unlimited_swipes),
                contentDescription = null,
                modifier = Modifier
                    .defaultMinSize(minWidth = 240.dp)
                    .weight(1f)
                    .height(59.dp)
            )
        }
    }
}

@Composable
private fun TransformCards(
    items: List<String>?,
    modifier: Modifier = Modifier,
    transforms: List<CardTransform> = defaultCardTransform
) {
    val shape = RoundedCornerShape(26.dp)

    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopCenter
    ) {
        transforms
            .mapIndexed { index, transform -> index to transform }
            .sortedBy { it.second.zIndex }
            .forEach { (index, transform) ->
                val item = items?.getOrNull(index)

                PhotoCard(
                    imageUrl = item,
                    transform = transform,
                    modifier = Modifier.zIndex(transform.zIndex),
                    shape = shape
                )
            }

    }
}

@Composable
private fun PhotoCard(
    imageUrl: String?,
    transform: CardTransform,
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(26.dp)
) {
    Card(
        modifier = modifier
            .width(transform.width)
            .height(transform.height)
            .offset(x = transform.offsetX, y = transform.offsetY)
            .rotate(transform.rotation)
            .border(4.dp, Color.White, shape),
        shape = shape
    ) {
        if (imageUrl != null) {
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                AsyncImage(
                    model = imageUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .hazeEffect {
                            blurRadius = 10.dp
                            blurEnabled = true
                        }
                        .fillMaxSize()
                )
            }
        } else {
            // 占位符
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Gray.copy(alpha = 0.3f))
            )
        }
    }
}


private val defaultCardTransform: List<CardTransform>
    @Composable get() = remember {
        listOf(
            // 右上角卡片
            CardTransform(
                offsetX = 60.dp,
                offsetY = 0.dp,
                rotation = 15f,
                zIndex = 1f,
                width = 144.dp,
                height = 204.dp
            ),
            // 左上角大卡片
            CardTransform(
                offsetX = (-60).dp,
                offsetY = (26.5).dp,
                rotation = -15f,
                zIndex = 2f,
                width = 143.dp,
                height = 203.dp
            ),
            // 中间小卡片
            CardTransform(
                offsetX = 60.dp,
                offsetY = 203.dp,
                rotation = 15f,
                zIndex = 3f,
                width = 152.dp,
                height = 152.dp
            ),
            // 左下角卡片
            CardTransform(
                offsetX = (-60).dp,
                offsetY = 220.dp,
                rotation = 15f,
                zIndex = 4f,
                width = 145.dp,
                height = 227.dp,
                brush = DEFAULT_BRUSH
            ),
            // 右下角小卡片
            CardTransform(
                offsetX = 45.dp,
                offsetY = 312.dp,
                rotation = -15f,
                zIndex = 5f,
                width = 111.dp,
                height = 143.dp,
                brush = DEFAULT_BRUSH
            ),
        )
    }


private val DEFAULT_BRUSH_COLOR = Color(0xFFFAF3F9)
private val DEFAULT_BRUSH = Brush.verticalGradient(
    colors = listOf(
        DEFAULT_BRUSH_COLOR.copy(0f),
        DEFAULT_BRUSH_COLOR.copy(1f)
    )
)

@Preview
@Composable
private fun DiscoverSwipeEmptyContentPreview() {
    AppTheme {
        DiscoverSwipeEmptyContent(
            items = listOf(
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            )
        )
    }
}