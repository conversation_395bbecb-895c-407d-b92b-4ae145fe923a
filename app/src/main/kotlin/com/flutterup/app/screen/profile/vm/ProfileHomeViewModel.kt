package com.flutterup.app.screen.profile.vm

import com.flutterup.app.screen.profile.state.ProfileUiState
import com.flutterup.app.utils.UserRepository
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.base.BaseRepository
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class ProfileHomeViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val unreadMonitor: UserUnreadMonitor,
    private val repository: ProfileRepository
) : BaseRepositoryViewModel() {

    private val _uiState: MutableStateFlow<ProfileUiState> = MutableStateFlow(ProfileUiState())

    val uiState = combine(
        _uiState,
        unreadMonitor.unreadCount,
        userRepository.userInfoState
    ) { ui, unread, userInfo ->
        ui.copy(
            newVisitorsCount = unread.visitorNewNum,
            newVisitorsHeadimg = unread.headList,

            isAly = userInfo?.isAly() ?: false,
            nickname = userInfo?.nickname,
            headimg = userInfo?.headImage,
            isVip = userInfo?.right?.vip == 1,
            pp = userInfo?.right?.privacyImage ?: 0,
            pv = userInfo?.right?.privacyVideo ?: 0,
            pingchat = userInfo?.right?.flashChat ?: 0,
            diamond = 0.00, //TODO 修改为真实的钻石
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )
}