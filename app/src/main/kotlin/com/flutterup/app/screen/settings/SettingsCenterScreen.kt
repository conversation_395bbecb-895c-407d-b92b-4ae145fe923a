@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppLineOptionText
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.developer.DeveloperRoute
import com.flutterup.app.screen.settings.state.SettingsCenterState
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType.ACCOUNT
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType.APP_ENVIRONMENT
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType.CLEAN_CACHE
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType.DEVELOP_MENU
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType.NOTIFICATION
import com.flutterup.app.screen.settings.vm.SettingsCenterViewModel

@Composable
fun SettingsCenterScreen() {
    val navController = LocalNavController.current
    val viewModel = hiltViewModel<SettingsCenterViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    SettingsCenterContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onOptionClick = {
            when(it) {
                ACCOUNT -> {
                    navController.navigate(AccountCenterRoute)
                }
                NOTIFICATION -> {
                    navController.navigate(SettingsNotificationRoute)
                }
                CLEAN_CACHE -> {
                    viewModel.cleanCache()
                }
                APP_ENVIRONMENT -> {
                    //TODO 显示切换环境的对话框
                }

                DEVELOP_MENU -> {
                    navController.navigate(DeveloperRoute)
                }
            }
        }
    )
}

@Composable
private fun SettingsCenterContent(
    uiState: SettingsCenterState,
    onBackClick: () -> Unit = {},
    onOptionClick: (SettingsCenterState.OptionType) -> Unit = {},
) {
    AppScaffold(
        title = {
            AppTitleText(stringResource(R.string.setting))
        },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier.fillMaxSize()
    ) { paddingValues ->
        Box(Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth().align(Alignment.TopCenter)
            )
        }

        LazyColumn(
            modifier = Modifier
                .padding(top = 18.dp)
                .padding(paddingValues)
                .fillMaxSize(),
            contentPadding = PaddingValues(horizontal = 15.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            items(uiState.options, key = { it.titleRes }) {
                val isProgressing = if (it == SettingsCenterState.OptionType.CLEAN_CACHE) uiState.isCleanCaching else false

                AppLineOptionText(
                    title = stringResource(it.titleRes),
                    iconDrawableRes = it.iconDrawableRes,
                    isProgressing = isProgressing,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                        .clickable(
                            enabled = !isProgressing,
                            onClick = { onOptionClick(it) },
                            role = Role.Button
                        )
                )
            }
        }
    }
}

@Preview
@Composable
private fun SettingsCenterScreenPreview() {
    AppTheme {
        SettingsCenterContent(
            uiState = SettingsCenterState(options = SettingsCenterState.OptionType.entries)
        )
    }
}