package com.flutterup.app.screen

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.ui.Modifier
import com.flutterup.app.databinding.ActivityImageCropperBinding
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.base.BaseViewActivity
import dagger.hilt.android.AndroidEntryPoint
import androidx.core.net.toUri
import com.canhub.cropper.CropImageView
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.base.utils.Timber
import java.io.File

@AndroidEntryPoint
class ImageCropperActivity : BaseViewActivity() {

    companion object {
        private const val KEY_PATH = "path"
        private const val KEY_OUTPUT_PATH = "output_path"
        private const val KEY_ASPECT_RATIO_X = "aspect_ratio_x"
        private const val KEY_ASPECT_RATIO_Y = "aspect_ratio_y"

        private const val DEFAULT_ASPECT_RATIO_X = 3
        private const val DEFAULT_ASPECT_RATIO_Y = 4

        fun createInstance(
            context: Context,
            path: String,
            outputPath: String = path,
            aspectRatioX: Int = DEFAULT_ASPECT_RATIO_X,
            aspectRatioY: Int = DEFAULT_ASPECT_RATIO_Y,
        ): Intent {
            val intent = Intent(context, ImageCropperActivity::class.java)

            intent.putExtra(KEY_PATH, path)
            intent.putExtra(KEY_OUTPUT_PATH, outputPath)
            intent.putExtra(KEY_ASPECT_RATIO_X, aspectRatioX)
            intent.putExtra(KEY_ASPECT_RATIO_Y, aspectRatioY)

            return intent
        }

        /**
         * 获取裁剪后的图片路径
         */
        fun getResult(intent: Intent): String {
            return intent.getStringExtra(KEY_OUTPUT_PATH) ?: ""
        }
    }

    private lateinit var binding: ActivityImageCropperBinding

    private lateinit var path: String
    private lateinit var outputPath: String

    private var aspectRatioX: Int = DEFAULT_ASPECT_RATIO_X

    private var aspectRatioY: Int = DEFAULT_ASPECT_RATIO_Y

    private lateinit var inputUri: Uri
    private lateinit var outputUri: Uri

    override fun getContentView(): View {
        binding = ActivityImageCropperBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        initParams()
        initCropperView()
        initContinue()

        binding.ivBack.setOnClickListener {
            setResult(RESULT_CANCELED)
            finish()
        }


    }

    private fun initParams() {
        path = intent.getStringExtra(KEY_PATH) ?: throw IllegalArgumentException("path is null")
        outputPath = intent.getStringExtra(KEY_OUTPUT_PATH) ?: path

        inputUri = File(path).toUri()
        outputUri = File(outputPath).toUri()

        aspectRatioX = intent.getIntExtra(KEY_ASPECT_RATIO_X, DEFAULT_ASPECT_RATIO_X)
        aspectRatioY = intent.getIntExtra(KEY_ASPECT_RATIO_Y, DEFAULT_ASPECT_RATIO_Y)
    }

    private fun initCropperView() {
        //设置裁剪的参数
        binding.cropImageView.apply{
            //设置裁剪的宽高比
            setAspectRatio(aspectRatioX, aspectRatioY)
            //设置裁剪是否开启指导线
            guidelines = CropImageView.Guidelines.OFF
        }


        binding.cropImageView.setOnCropImageCompleteListener { view, result ->
            if (!result.isSuccessful) {
                Timber.showToast(getString(R.string.image_cropper_error))
                return@setOnCropImageCompleteListener
            }

            val resultIntent = Intent()
            resultIntent.putExtra(KEY_OUTPUT_PATH, outputPath)
            setResult(RESULT_OK, resultIntent)
            finish()
        }

        try {
//            val bitmap = BitmapFactory.decodeStream(contentResolver.openInputStream(inputUri))
//            if (bitmap != null) {
//                binding.cropImageView.setImageBitmap(bitmap)
//            }
            binding.cropImageView.setImageUriAsync(inputUri)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initContinue() {
        binding.composeView.setContent {
            AppTheme {
                AppContinueButton(
                    onClick = {
                        binding.cropImageView.croppedImageAsync(
                            saveCompressFormat = Bitmap.CompressFormat.PNG,
                            saveCompressQuality = 100,
                            customOutputUri = outputUri
                        )
                    },
                    enabled = true,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}