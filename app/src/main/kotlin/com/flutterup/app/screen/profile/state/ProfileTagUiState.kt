package com.flutterup.app.screen.profile.state

import com.flutterup.app.model.Tag

data class ProfileTagUiState(
    val currentInterests: List<Tag> = emptyList(),
    val confirmedInterests: List<Tag> = emptyList(),
    val interests: List<Tag> = emptyList(),
    val limit: Int = MAX_LIMIT,
) {
    val limitText: String
        get() = "${currentInterests.size}/$limit"

    val canAddMore: Boolean
        get() = currentInterests.size < limit

    companion object {
        private const val MAX_LIMIT = 5
    }
}