package com.flutterup.app.screen.relate.vm

import com.flutterup.app.screen.relate.state.RelateHomeState
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.screen.relate.state.WinksSent
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject


@HiltViewModel
class RelateHomeViewModel @Inject constructor(
    unreadMonitor: UserUnreadMonitor,
) : BaseViewModel() {
    val uiState: StateFlow<RelateHomeState> = unreadMonitor.unreadCount.map {
        RelateHomeState(
            titles = listOf(
                WinksReceived(it.wlmNewNum),
                WinksSent.EMPTY,
                Visitors(it.visitorNewNum)
            )
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        RelateHomeState()
    )
}