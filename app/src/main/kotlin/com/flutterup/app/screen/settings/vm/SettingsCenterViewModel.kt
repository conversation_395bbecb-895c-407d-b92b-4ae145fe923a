package com.flutterup.app.screen.settings.vm

import android.content.Context
import coil3.imageLoader
import com.flutterup.app.R
import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.app.network.environment.DebugEnvironmentProvider
import com.flutterup.app.network.environment.DebugNoSubAccountEnvironmentProvider
import com.flutterup.app.network.environment.ProdEnvironmentProvider
import com.flutterup.app.network.environment.ProdNoSubAccountEnvironmentProvider
import com.flutterup.app.screen.settings.state.SettingsCenterState
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType
import com.flutterup.base.AppDirs
import com.flutterup.base.BaseViewModel
import com.flutterup.base.Dirs
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class SettingsCenterViewModel @Inject constructor(
    environmentProvider: AppEnvironmentProvider,
    @ApplicationContext private val applicationContext: Context,
    @Dirs(AppDirs.APP_CACHE) private val appCacheDir: File,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(SettingsCenterState.EMPTY)
    val uiState: MutableStateFlow<SettingsCenterState> = _uiState

    init {
        when(environmentProvider) {
            is DebugNoSubAccountEnvironmentProvider, is DebugEnvironmentProvider -> {
                _uiState.update { it.copy(options = OptionType.entries) }
            }
            is ProdNoSubAccountEnvironmentProvider, is ProdEnvironmentProvider -> {
                _uiState.update {
                    it.copy(
                        options = listOf(OptionType.ACCOUNT, OptionType.NOTIFICATION, OptionType.CLEAN_CACHE)
                    )
                }
            }
        }
    }

    fun cleanCache() {
        scope.launch(Dispatchers.IO) {
            _uiState.update { it.copy(isCleanCaching = true) }

            val imageLoader = applicationContext.imageLoader
            // 1) clear memory cache
            imageLoader.memoryCache?.clear()
            // 2) clear file cache
            imageLoader.diskCache?.clear()

            //clear app cache
            appCacheDir.deleteRecursively()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isCleanCaching = false) }
            Timber.showToast(applicationContext.getString(R.string.cache_cleared))
        }
    }
}