package com.flutterup.app.screen.gift

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.flutterup.base.utils.JsonUtils
import com.flutterup.gifts.GiftPreloadManager
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.gifts.entity.PreloadProgress
import com.flutterup.gifts.entity.PreloadResult
import com.squareup.moshi.JsonClass
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class GiftViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val giftPreloadManager: GiftPreloadManager,
    private val jsonUtils: JsonUtils,
) : ViewModel() {

    private var data: Gifts? = null

    val uiState = giftPreloadManager.preloadProgress
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), PreloadProgress.Idle)
        .map { progress ->
            when (progress) {
                is PreloadProgress.Idle -> GiftUIState.Idle
                is PreloadProgress.InProgress -> GiftUIState.Preloading(progress)
                is PreloadProgress.Completed -> when (val result = progress.result) {
                    is PreloadResult.Success -> GiftUIState.Success(result)
                    is PreloadResult.PartialSuccess -> GiftUIState.PartialSuccess
                    is PreloadResult.Error -> GiftUIState.Error
                }
            }
        }

    init {
        data = loadGiftsFromAssets()
    }

    fun preloadGifts() {
        if (giftPreloadManager.isPreloading()) return //防止重复加载

        if (data == null) {
            data = loadGiftsFromAssets()
        }

        data?.gifts?.let {
            giftPreloadManager.preloadGifts(it)
        }
    }

    private fun loadGiftsFromAssets(): Gifts? {
        return context.assets.open("gift.json").bufferedReader().use { it.readText() }
            .let { jsonUtils.fromJson(json = it, clazz = Gifts::class.java) }
    }

    @JsonClass(generateAdapter = true)
    data class Gifts(
        val gifts: List<GiftResourceInfo>
    )
}

sealed interface GiftUIState {
    data object Idle : GiftUIState

    data class Preloading(val progress: PreloadProgress.InProgress) : GiftUIState

    data object PartialSuccess : GiftUIState

    data class Success(val result: PreloadResult.Success) : GiftUIState

    data object Error : GiftUIState
}