package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.model.UserActionType
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class UserActionRepository @Inject constructor(
    private val apiService: ApiService,
    private val userRepository: UserRepository
) : BaseRepository() {

    suspend fun dislike(
        userId: String,
        from: AppFrom = AppFrom.Unknown,
        cardFlag: UserActionCardType = UserActionCardType.Default,
    ) = userAction(userId, UserActionType.Dislike, from, cardFlag)

    suspend fun like(
        userId: String,
        from: AppFrom = AppFrom.Unknown,
        cardFlag: UserActionCardType = UserActionCardType.Default,
    ) = userAction(userId, UserActionType.Like, from, cardFlag)

    suspend fun userAction(
        userId: String,
        type: UserActionType,
        from: AppFrom = AppFrom.Unknown,
        cardFlag: UserActionCardType = UserActionCardType.Default,
    ): Boolean {
        val result = apiService.userAction(
            userId = userId,
            type = type.value,
            from = from.value,
            cardFlag = cardFlag.value
        )

        if (result.isSuccess) {
            result.data?.right?.let { right ->
                userRepository.updateUserRight(right)
            }
        }

        return result.isSuccess
    }
}