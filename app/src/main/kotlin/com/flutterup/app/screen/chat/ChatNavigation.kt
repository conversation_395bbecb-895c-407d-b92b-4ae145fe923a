package com.flutterup.app.screen.chat

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.flutterup.app.navigation.BottomNavRoute
import kotlinx.serialization.Serializable

@Serializable data object ChatRoute : BottomNavRoute

@Serializable data object PingChatRoute

fun NavGraphBuilder.chatGraph() {
    composable(route = PingChatRoute::class) {
        PingChatScreen()
    }
}