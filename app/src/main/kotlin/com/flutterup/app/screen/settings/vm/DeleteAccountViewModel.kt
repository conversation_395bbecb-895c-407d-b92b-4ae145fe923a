package com.flutterup.app.screen.settings.vm

import android.content.Context
import androidx.room.Index
import com.flutterup.app.screen.settings.state.DeleteAccountState
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class DeleteAccountViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val repository: AccountRepository,
    private val userRepository: UserRepository
) : BaseRepositoryViewModel(repository) {

    private val _uiState: MutableStateFlow<DeleteAccountState> = MutableStateFlow(DeleteAccountState.createDeleteAccountState(context))

    val uiState: StateFlow<DeleteAccountState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateSelectedReasonIndex(index: Int) {
        _uiState.update { it.copy(selectedReasonIndex = index) }
    }

    fun updateCustomReason(reason: String) {
        _uiState.update { it.copy(customReason = reason) }
    }

    fun submit() {
        val state = uiState.value
        if (state.selectedReasonIndex == -1 && state.customReason.isEmpty()) return

        scope.launchWithLoading {
            val result = repository.deleteAccount()

            if (result) { //删除账号成功
                userRepository.logout()
            }
        }
    }
}
