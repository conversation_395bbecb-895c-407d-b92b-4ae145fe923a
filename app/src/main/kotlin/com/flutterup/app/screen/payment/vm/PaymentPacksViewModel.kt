package com.flutterup.app.screen.payment.vm

import android.content.Context
import com.flutterup.app.R
import com.flutterup.app.model.PaymentPacksOrderBy
import com.flutterup.app.screen.payment.state.PacksQuantity
import com.flutterup.app.screen.payment.state.PaymentPacksUiState
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class PaymentPacksViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PaymentPacksUiState.TEST)

    val uiState = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun init(orderBy: PaymentPacksOrderBy) {
        _uiState.update { it.copy(orderBy = orderBy) }
    }

    fun updateCurrentExchangeQuantity(newExchangeQuantity: Int?) {
        if (newExchangeQuantity == null) {
            Timber.showToast(quantityRangeText)
            _uiState.update { it.copy(currentExchangeQuantity = PacksQuantity.MIN) }
            return
        }

        if (newExchangeQuantity !in PacksQuantity.RANGE) {
            Timber.showToast(quantityRangeText)
        }

        _uiState.update { it.copy(currentExchangeQuantity = newExchangeQuantity.coerceIn(PacksQuantity.RANGE)) }
    }

    fun updateCurrentProductId(id: String?) {
        _uiState.update { it.copy(currentProductId = id) }
    }

    fun exchange() {
        Timber.showToast("点击了继续兑换, 商品ID：${uiState.value.currentProductId}, 兑换数量：${uiState.value.currentExchangeQuantity}, 需要钻石：${uiState.value.neededDiamonds}")
    }

    private val quantityRangeText = context.getString(
        R.string.packs_exchange_limit, PacksQuantity.MIN, PacksQuantity.MAX
    )
}
