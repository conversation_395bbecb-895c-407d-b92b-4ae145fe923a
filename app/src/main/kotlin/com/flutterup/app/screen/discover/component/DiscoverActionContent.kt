package com.flutterup.app.screen.discover.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.theme.ShadowAmbientPrimary
import com.flutterup.app.design.theme.ShadowSpotPrimary
import com.flutterup.app.screen.discover.state.DiscoverState
import com.valentinilk.shimmer.Shimmer

@Composable
fun DiscoverActionContent(
    uiState: DiscoverState,
    shimmerOnPrimary: Shimmer,
    onDislikeClick: () -> Unit = {},
    onLikeClick: () -> Unit = {},
    onPingChatClick: () -> Unit = {},
) {
    val itemModifier = Modifier
        .shadow(
            elevation = 10.dp,
            spotColor = ShadowSpotPrimary,
            ambientColor = ShadowAmbientPrimary
        )

    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(38.dp))

        ShimmerContent(
            uiState = uiState,
            shimmer = shimmerOnPrimary,
            modifier = Modifier
                .size(54.dp)
                .clip(CircleShape)
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_dislike),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .then(itemModifier)
                    .clickable(onClick = onDislikeClick)
            )
        }

        Spacer(modifier = Modifier.width(18.dp))

        ShimmerContent(
            uiState = uiState,
            shimmer = shimmerOnPrimary,
            modifier = Modifier
                .size(54.dp)
                .clip(CircleShape)
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_like),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .then(itemModifier)
                    .clickable(onClick = onLikeClick)
            )
        }

        Spacer(modifier = Modifier.width(21.dp))

        ShimmerContent(
            uiState = uiState,
            shimmer = shimmerOnPrimary,
            modifier = Modifier
                .size(width = 160.dp, height = 42.dp)
                .clip(RoundedCornerShape(21.dp))
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_ping_chat),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .then(itemModifier)
                    .size(width = 160.dp, height = 42.dp)
                    .clickable(onClick = onPingChatClick)
            )
        }
    }
}
