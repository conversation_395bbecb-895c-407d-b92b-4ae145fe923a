@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.payment

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.AppVipButtonPrimaryColors
import com.flutterup.app.design.theme.BackgroundTheme
import com.flutterup.app.design.theme.LocalBackgroundTheme
import com.flutterup.app.design.theme.PinkPrimary
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.SubscriptionBenefitEntity
import com.flutterup.app.model.SubscriptionItem
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.payment.component.SubscriptionBenefits
import com.flutterup.app.screen.payment.component.SubscriptionStores
import com.flutterup.app.screen.payment.state.PaymentSubscriptUiState
import com.flutterup.app.screen.payment.vm.PaymentSubscriptionViewModel

@Composable
fun PaymentSubscriptionScreen(
    eventFrom: AppPaymentFrom,
    expireTime: Long?
) {
    val navController = LocalNavController.current
    val backgroundTheme = LocalBackgroundTheme.current
    val viewModel = hiltViewModel<PaymentSubscriptionViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()


    SubscriptionContent(
        uiState = uiState,
        backgroundTheme = backgroundTheme,
        onBackClick = navController::popBackStack,
        onItemClick = { viewModel.updateSelected(it) },
        onContinueClick = { viewModel.pay() }
    )
}


@Composable
private fun SubscriptionContent(
    uiState: PaymentSubscriptUiState,
    backgroundTheme: BackgroundTheme,
    onBackClick: () -> Unit = {},
    onItemClick: (Int) -> Unit = {},
    onContinueClick: () -> Unit = {},
) {
    val crossHeight: Dp = 32.dp
    val scrollState = rememberScrollState()

    AppScaffold(
        title = {},
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        val paddingBottom = paddingValues.calculateBottomPadding() + 13.dp

        Box {
            Column(
                modifier = Modifier
                    .padding(bottom = 50.dp)
                    .padding(bottom = paddingBottom)
                    .fillMaxSize()
                    .verticalScroll(scrollState)
            ) {
                SubscriptionBenefits(
                    modifier = Modifier,
                    uiState = uiState,
                    paddingValues = paddingValues,
                    crossHeight = crossHeight,
                )

                SubscriptionStores(
                    modifier = Modifier,
                    uiState = uiState,
                    crossHeight = crossHeight,
                    backgroundColor = backgroundTheme.color,
                    onItemClick = onItemClick,
                )
            }

            AppContinueButton(
                onClick = onContinueClick,
                enabled = uiState.selected in uiState.items.indices,
                isLoading = uiState.isLoading,
                colors = AppVipButtonPrimaryColors,
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(bottom = paddingBottom)
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}

@Preview
@Composable
private fun SubscriptionScreenPreview() {
    AppTheme {
        SubscriptionContent(
            uiState = PaymentSubscriptUiState(
                benefits = List(7) {
                    SubscriptionBenefitEntity(
                        icon = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        iconUnselect = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        title = "test$it, aewae",
                        desc = "test$it, tetettetetetetteasddklamkmkzmkcmxko1ommkmczmx1mklmadkmmmdm",
                        background = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
                    )
                },
                items = List(3) {
                    SubscriptionItem(
                        time = it.toString(),
                        unit = "Month",
                        save = "SAVE 63%",
                        price = "$ 99.99",
                        average = "or 7.99 $ / w.",
                        hot = if (it == 1) 1 else 0,
                        prodId = "1",
                        prodType = "1",
                        subscribing = 0,
                        name = "test$it",
                    )
                },
                description = "temp"
            ),
            backgroundTheme = BackgroundTheme(color = PinkPrimary, tonalElevation = 0.dp)
        )
    }
}