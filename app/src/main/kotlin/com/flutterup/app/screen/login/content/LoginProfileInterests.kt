package com.flutterup.app.screen.login.content

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LinePrimary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.model.Tag


@Composable
fun LoginProfileInterests(
    modifier: Modifier = Modifier,
    interests: List<Tag> = emptyList(),
    onClick: () -> Unit,
) {
    Column(
        modifier = modifier.noRippleClickable(onClick = onClick)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.profile_interest_label),
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 28.sp,
                    fontWeight = FontWeight.W500,
                    color = Color.Black,
                )
            )

            Icon(
                painter = painterResource(R.drawable.ic_arrow_down),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(24.dp)
            )
        }

        if (interests.isEmpty()) {
            Spacer(modifier = Modifier.height(2.dp))

            Text(
                text = stringResource(R.string.profile_introduction_placeholder),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack666,
                )
            )
        } else {
            Spacer(modifier = Modifier.height(6.dp))

            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                val itemModifier = Modifier
                    .border(
                        width = 1.dp,
                        color = PurplePrimary,
                        shape = RoundedCornerShape(8.dp)
                    )
                val textModifier = Modifier.padding(horizontal = 12.dp, vertical = 10.dp)
                val textStyle = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.W400,
                    color = PurplePrimary,
                )

                interests.forEach {
                    Box(modifier = itemModifier) {
                        Text(
                            text = it.title.orEmpty(),
                            style = textStyle,
                            modifier = textModifier
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(10.dp))

        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 1.dp,
            color = LinePrimary
        )
    }
}

@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun LoginProfileInterestsPreview() {
    AppTheme {
        LoginProfileInterests(
            onClick = {},
            interests = listOf(
                Tag(id = 1, title = "test1"),
                Tag(id = 2, title = "test2"),
                Tag(id = 3, title = "aklkamdksl"),
                Tag(id = 4, title = "naskdnksdnakd"),
                Tag(id = 5, title = "sdjnkdnaskdjn"),
                Tag(id = 6, title = "jknadndasnk")
            )
        )
    }
}