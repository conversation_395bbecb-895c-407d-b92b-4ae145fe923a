@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.payment

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.text.buildAppAnnotatedString
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.AppVipButtonPrimaryColors
import com.flutterup.app.design.theme.PaymentBackgroundGradientEnd
import com.flutterup.app.design.theme.PaymentBackgroundGradientStart
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextBrownVipPrimary
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.PaymentDiamondsItem
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.payment.state.PaymentDiamondsUiState
import com.flutterup.app.screen.payment.vm.PaymentDiamondsViewModel
import com.flutterup.base.utils.toDecimalSubstring


@Composable
fun PaymentDiamondsScreen() {
    val navController = LocalNavController.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val viewModel = hiltViewModel<PaymentDiamondsViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = navController::popBackStack,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        PaymentDiamondsContent(
            uiState = uiState,
            onItemClick = { viewModel.updateCurrentDiamondsPackId(it.id) },
            onContinueClick = { viewModel.pay() }
        )
    }
}

@Composable
private fun PaymentDiamondsContent(
    modifier: Modifier = Modifier,
    uiState: PaymentDiamondsUiState,
    onItemClick: (PaymentDiamondsItem) -> Unit = {},
    onContinueClick: () -> Unit = {},
) {
    var boxHeightPx by remember { mutableIntStateOf(0) }

    val backgroundBrush = Brush.verticalGradient(
        colors = listOf(
            PaymentBackgroundGradientStart,
            PaymentBackgroundGradientEnd
        ),
        startY = 0f,
        endY = boxHeightPx * 0.4f //end为整个页面的40%
    )

    val overlayBitmap: ImageBitmap = ImageBitmap.imageResource(id = R.mipmap.ic_packs_top_lights)

    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .background(backgroundBrush)
            .verticalScroll(rememberScrollState())
            .drawWithContent {
                drawContent()

                // 叠加 mipmap 图片
                drawImage(
                    image = overlayBitmap,
                    dstSize = IntSize(
                        width = size.width.toInt(),
                        height = with(density) { 210.dp.toPx() }.toInt()
                    ),
                    blendMode = BlendMode.Overlay
                )
            }
            .onSizeChanged {
                boxHeightPx = it.height
            }
    ) {
        Column(
            modifier = Modifier.fillMaxWidth().padding(top = 20.dp)
        ) {
            Text(
                text = stringResource(R.string.diamond_balance),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBrownVipPrimary,
                ),
                modifier = Modifier.padding(start = 17.dp)
            )

            Text(
                text = uiState.diamonds.toDecimalSubstring(isReduce = false),
                style = TextStyle(
                    fontSize = 25.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.W900,
                    color = TextBrownVipPrimary,
                ),
                modifier = Modifier.padding(start = 17.dp)
            )

            DiamondsPacksList(
                modifier = Modifier.padding(top = 20.dp, start = 12.dp, end = 12.dp),
                uiState = uiState,
                onItemClick = onItemClick
            )

            if (uiState.description1 != null) {
                Text(
                    text = uiState.description1,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray999,
                        textAlign = TextAlign.Justify,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 27.dp, start = 17.dp, end = 17.dp)
                )
            }

            if (uiState.description2 != null) {
                Text(
                    text = uiState.description2,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray999,
                        textAlign = TextAlign.Justify,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 27.dp, start = 17.dp, end = 17.dp)
                )
            }

            AppContinueButton(
                onClick = onContinueClick,
                enabled = uiState.currentDiamondsPackId != null && !uiState.isLoading,
                isLoading = uiState.isLoading,
                colors = AppVipButtonPrimaryColors,
                modifier = Modifier
                    .padding(top = 39.dp)
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}

@Composable
private fun DiamondsPacksList(
    modifier: Modifier = Modifier,
    uiState: PaymentDiamondsUiState,
    onItemClick: (PaymentDiamondsItem) -> Unit
) {
    val cornerRadius = 10.dp
    val shape = RoundedCornerShape(cornerRadius)
    var widthPx by remember { mutableIntStateOf(0) }
    var heightPx by remember { mutableIntStateOf(0) }
    val borderWidth = 1.dp

    val selectedBackgroundBrush1 = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFFD99F),
            Color.White,
        ),
        startY = 0f,
        endY = heightPx.toFloat()
    )

    val selectedBackgroundBrush2 = Brush.radialGradient(
        colors = listOf(
            Color.White,
            Color.Transparent,
        ),
        center = Offset(
            x = widthPx / 2f,
            y = 0f
        )
    )

    val basicModifier = Modifier
        .fillMaxWidth()
        .height(60.dp)
        .clip(shape)
        .onSizeChanged {
            widthPx = it.width
            heightPx = it.height
        }

    val normalModifier = Modifier
        .background(Color.White, shape = shape)
        .border(borderWidth, DIAMONDS_PACKS_BORDER_NORMAL, shape)

    val selectedModifier = Modifier
        .background(selectedBackgroundBrush1, shape = shape)
        .drawWithContent {
            drawRect(
                brush = selectedBackgroundBrush2,
                blendMode = BlendMode.Screen,
            )
            drawContent()
        }
        .border(borderWidth, DIAMONDS_PACKS_BORDER_SELECTED, shape)

    val spanStyleDiamonds = SpanStyle(
        fontSize = 20.sp,
        fontWeight = FontWeight(800),
        color = Color(0xFFCA8A5A),
    )
    val spanStyleCoin = SpanStyle(
        fontSize = 14.sp,
        fontWeight = FontWeight.W400,
        color = TextBlack666,
    )

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        repeat(uiState.diamondsPacks.size) { index ->
            val item = uiState.diamondsPacks[index]
            val isSelected = uiState.currentDiamondsPackId == item.id
            DiamondsPackItem(
                modifier = basicModifier.then(if (isSelected) selectedModifier else normalModifier),
                item = item,
                selected = uiState.currentDiamondsPackId == item.id,
                spanStyleDiamonds = spanStyleDiamonds,
                spanStyleCoin = spanStyleCoin,
                cornerRadius = cornerRadius,
                onItemClick = onItemClick,
            )
        }
    }
}

@Composable
private fun DiamondsPackItem(
    modifier: Modifier,
    item: PaymentDiamondsItem,
    selected: Boolean,
    spanStyleDiamonds: SpanStyle,
    spanStyleCoin: SpanStyle,
    cornerRadius: Dp,
    onItemClick: (PaymentDiamondsItem) -> Unit
) {

    val coin = stringResource(R.string.coin)

    val annotatedString = buildAppAnnotatedString {
        append(text = item.price.orEmpty(), spanStyle = spanStyleDiamonds)

        append(text = "${item.unit.orEmpty()}/", spanStyle = spanStyleCoin)

        append(text = item.diamond.toDecimalSubstring(), spanStyle = spanStyleDiamonds)

        append(text = coin, spanStyle = spanStyleCoin)
    }

    Box(
        modifier = modifier.noRippleClickable(onClick = { onItemClick(item) })
    ) {
        when {
            item.popular == 1 -> PopularLabel(cornerRadius) //是流行状态

            item.hot == 1 -> HotLabel(cornerRadius) //是热门状态
        }

        Row(
            modifier = Modifier
                .matchParentSize()
                .padding(horizontal = 15.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AsyncImage(
                model = item.icon,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
            )

            Spacer(modifier = Modifier.width(10.dp))

            Column {
                Text(
                    text = item.name.orEmpty(),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W700,
                        color = if (selected) TextBrownVipPrimary else TextBlack666,
                    )
                )

                if (item.subName != null) {
                    Text(
                        text = item.subName,
                        style = TextStyle(
                            fontSize = 10.sp,
                            lineHeight = 10.sp,
                            fontWeight = FontWeight.W900,
                            color = TextGray999,
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            Text(text = annotatedString, style = TextStyle(lineHeight = 22.sp))
        }
    }
}

@Composable
private fun BoxScope.PopularLabel(cornerRadius: Dp) {
    Box(
        modifier = Modifier
            .background(
                color = Color(0xFFDA8964),
                shape = RoundedCornerShape(
                    topStart = cornerRadius,
                    bottomEnd = cornerRadius,
                )
            )
            .padding(horizontal = 4.dp, vertical = 3.dp)
            .align(Alignment.TopStart),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.popular).uppercase(),
            style = TextStyle(
                fontSize = 10.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight.W800,
                fontStyle = FontStyle.Italic,
                color = Color.White,
            )
        )
    }
}

@Composable
private fun BoxScope.HotLabel(cornerRadius: Dp) {
    val brush = Brush.linearGradient(
        colors = listOf(
            Color.Black,
            Color.White,
            Color(0xFFD5D5D5),
            Color.Black,
            Color.White,
            Color.Black,
        ),
        start = Offset(-10f, 0f),
        end = Offset(150f, -50f),
    )

    Box(
        modifier = Modifier
            .background(
                color = Color(0xFFDF3838),
                shape = RoundedCornerShape(
                    topStart = cornerRadius,
                    bottomEnd = cornerRadius,
                )
            )
            .drawWithContent {
                drawContent()

                // 再画一个 Overlay 混合色
                drawRect(
                    brush = brush,
                    blendMode = BlendMode.Overlay
                )
            }
            .padding(horizontal = 4.dp, vertical = 3.dp)
            .align(Alignment.TopStart),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.hot_sale).uppercase(),
            style = TextStyle(
                fontSize = 10.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight.W800,
                fontStyle = FontStyle.Italic,
                color = Color.White,
            )
        )
    }
}

private val DIAMONDS_PACKS_BORDER_NORMAL = Color(0xFFECECEC)
private val DIAMONDS_PACKS_BORDER_SELECTED = Color(0xFFCC6C4D)


@Preview
@Composable
private fun PaymentPacksScreenPreview() {
    AppTheme {
        Box(
            Modifier
                .fillMaxSize()
                .background(Color.White),
            contentAlignment = Alignment.BottomCenter
        ) {
            PaymentDiamondsContent(
                uiState = PaymentDiamondsUiState.TEST,
            )
        }
    }
}