package com.flutterup.app.screen.settings

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import androidx.navigation.navigation
import kotlinx.serialization.Serializable


@Serializable data object SettingsBaseRoute

@Serializable data object SettingsRoute

@Serializable data object AccountCenterRoute

@Serializable data object ChangePwdRoute

@Serializable data object DeleteAccountRoute

@Serializable data object SettingsNotificationRoute

fun NavGraphBuilder.settingsGraph() {
    navigation<SettingsBaseRoute>(startDestination = SettingsRoute::class) {
        composable(route = SettingsRoute::class) {
            SettingsCenterScreen()
        }

        composable(route = AccountCenterRoute::class) {
            AccountCenterScreen()
        }

        composable(route = ChangePwdRoute::class) {
            ChangePasswordScreen()
        }
        
        composable(route = DeleteAccountRoute::class) {
            DeleteAccountScreen()
        }

        composable(route = SettingsNotificationRoute::class) {
            SettingsNotificationScreen()
        }
    }
}