@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.relate

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PinkPrimary
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.relate.component.RelateHomePager
import com.flutterup.app.screen.relate.state.RelateHomeState
import com.flutterup.app.screen.relate.state.RelateTitle
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.screen.relate.state.WinksSent
import com.flutterup.app.screen.relate.vm.RelateHomeViewModel


@Composable
fun RelateScreen(index: Int? = null) {
    val innerPadding = LocalInnerPadding.current

    val viewModel: RelateHomeViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState(initialPage = uiState.initialPage) { uiState.titles.size }

    LaunchedEffect(index) {
        if (index != null) {
            val index = uiState.titles.indexOfFirst { it.index == index }
            if (index != -1) {
                pagerState.scrollToPage(index)
            }
        }
    }

    RelateScreenContent(
        innerPadding = innerPadding,
        uiState = uiState,
        pagerState = pagerState,
        pageContent = { index, item ->
            when (item) {
                is WinksReceived -> WinksReceivedScreen()
                is WinksSent -> WinksSentScreen()
                is Visitors -> VisitorScreen()
            }
        }
    )
}

@Composable
private fun RelateScreenContent(
    innerPadding: PaddingValues,
    uiState: RelateHomeState,
    pagerState: PagerState = rememberPagerState(initialPage = uiState.initialPage) { uiState.titles.size },
    pageContent: @Composable PagerScope.(page: Int, item: RelateTitle) -> Unit = { _, _ -> }
) {
    AppScaffold(
        canGoBack = false,
        title = { },
        onBackClick = { },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = innerPadding.calculateBottomPadding())
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )


            Column(Modifier.fillMaxSize().padding(top = innerPadding.calculateTopPadding())) {
                Text(
                    text = stringResource(R.string.connections),
                    style = TextStyle(
                        fontSize = 24.sp,
                        lineHeight = 28.sp,
                        fontWeight = FontWeight.W800,
                        fontStyle = FontStyle.Italic,
                        color = Color.White,
                    ),
                    modifier = Modifier.padding(start = 21.dp, top = 4.dp)
                )

                Spacer(Modifier.height(10.dp))

                RelateHomePager(
                    uiState = uiState,
                    pagerState = pagerState,
                    pageContent = pageContent
                )
            }
        }
    }
}


@Preview
@Composable
private fun RelateScreenPreview() {
    AppTheme {
        RelateScreenContent(
            innerPadding = PaddingValues(),
            uiState = RelateHomeState()
        )
    }
}