package com.flutterup.app.screen.settings.vm

import android.content.Context
import com.flutterup.app.R
import com.flutterup.app.screen.settings.state.ChangePwdState
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject


@HiltViewModel
class ChangePwdViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val repository: AccountRepository,
    private val userRepository: UserRepository
) : BaseRepositoryViewModel(repository) {

    private val _uiState = MutableStateFlow(ChangePwdState())

    val uiState: StateFlow<ChangePwdState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )


    fun updatePassword(password: String) {
        _uiState.update { it.copy(password = password) }
    }

    fun togglePasswordVisibility() {
        _uiState.update { it.copy(isPasswordVisible = !it.isPasswordVisible) }
    }

    fun updateConfirmedPassword(confirmedPassword: String) {
        _uiState.update { it.copy(confirmedPassword = confirmedPassword) }
    }

    fun toggleConfirmedPasswordVisibility() {
        _uiState.update { it.copy(isConfirmedPasswordVisible = !it.isConfirmedPasswordVisible) }
    }

    fun submit() {
        val uiState = uiState.value
        if (!uiState.isPasswordValid || !uiState.isConfirmPasswordValid) return

        if (uiState.password != uiState.confirmedPassword) {
            Timber.showToast(context.getString(R.string.password_not_match_error))
            return
        }

        scope.launchWithLoading {
            val result = repository.changePassword(uiState.password)
            if (result) {
                userRepository.logout()
            }
        }
    }
}