package com.flutterup.app.screen.login.state

import com.flutterup.app.model.Tag
import com.flutterup.base.BaseState


private const val MAX_MEDIA_COUNT = 6

data class LoginProfileStep2UIState(
    val sign: String = "",
    val tags: List<Tag> = emptyList(),
    val mediaStatuses: List<MediaStatus> = List(MAX_MEDIA_COUNT) { MediaStatus.Idle },
    override val isLoading: Boolean = false,
) : BaseState {

    /**
     * 是否可以继续
     */
    val isContinueEnabled: Boolean
        get() = sign.isNotEmpty() || tags.isNotEmpty() || mediaStatuses.any { it is MediaStatus.Success }

    val firstIdleIndex: Int
        get() = mediaStatuses.indexOfFirst { it is MediaStatus.Idle }


    sealed interface MediaStatus {
        object Idle : MediaStatus
        object Loading : MediaStatus

        data class Success(val url: String) : MediaStatus
    }
}