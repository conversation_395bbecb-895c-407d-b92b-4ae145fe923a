package com.flutterup.app.screen.relate.component.starring

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.StartOffset
import androidx.compose.animation.core.StartOffsetType
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.relate.state.VisitorRandom
import dev.chrisbanes.haze.hazeEffect
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun StarRing(
    modifier: Modifier = Modifier,
    config: StarRingConfig = StarRingConfigDefaults.defaults,
    ringAvatars: List<VisitorRandom> = emptyList(),
    centerContent: @Composable () -> Unit = {},
) {
    val density = LocalDensity.current

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        //绘制星环
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawRings(config, density)
        }

        //中心头像
        centerContent()

        //绘制星环上的头像
        for (index in config.rings.indices) {
            val ringConfig = config.rings[index]
            val start = index * ringConfig.maxAvatarCount
            val end = start + ringConfig.maxAvatarCount

            val avatarsInThisRing = if (start >= ringAvatars.size) {
                emptyList()
            } else {
                ringAvatars.subList(start, end.coerceAtMost(ringAvatars.size))
            }

            Ring(
                key = index,
                density = density,
                config = ringConfig,
                modifier = Modifier.fillMaxSize(),
                ringAvatars = avatarsInThisRing
            )
        }
    }
}

private fun DrawScope.drawRings(config: StarRingConfig, density: Density) {
    for (ringConfig in config.rings) {
        val radiusPx = with(density) { ringConfig.radius.toPx() }
        val borderWidthPx = with(density) { ringConfig.broderWidth.toPx() }

        drawCircle(
            color = ringConfig.color,
            radius = radiusPx,
            center = center,
            style = Stroke(width = borderWidthPx)
        )
    }
}

@Composable
private fun Ring(
    key: Any,
    config: RingConfig,
    density: Density,
    modifier: Modifier = Modifier,
    ringAvatars: List<VisitorRandom> = emptyList(),
) {
    if (ringAvatars.isEmpty() && config.ringStars.isEmpty()) return //空列表不进行操作

    // 旋转动画
    val infiniteTransition = rememberInfiniteTransition(label = "ring_rotation_$key")
    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = config.rotationDuration,
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation_angle"
    )

    val hazeModifier = if (config.enableBlur) {
        Modifier.hazeEffect {
            blurRadius = 5.dp
            blurEnabled = true
        }
    } else {
        Modifier
    }

    Box(
        modifier = modifier
            .then(if (config.enableRotation) Modifier.rotate(rotationAngle) else Modifier),
        contentAlignment = Alignment.Center
    ) {
        val radiusPx = with(density) { config.radius.toPx() }

        repeat(ringAvatars.size) { index ->
            val avatar = ringAvatars[index]
            val angle = config.avatarStartAngle + index * config.avatarAngleOffset

            val x = radiusPx * cos(Math.toRadians(angle.toDouble())).toFloat()
            val y = radiusPx * sin(Math.toRadians(angle.toDouble())).toFloat()
            val factor = config.scaleFactor * index

            RingAvatar(
                visitor = avatar,
                config = config,
                offset = Offset(x, y),
                factor = factor,
                density = density,
                hazeModifier = hazeModifier
            )
        }

        for (star in config.ringStars) {
            val x = radiusPx * cos(Math.toRadians(star.defaultAngle.toDouble())).toFloat()
            val y = radiusPx * sin(Math.toRadians(star.defaultAngle.toDouble())).toFloat()

            RingStar(
                config = star,
                offset = Offset(x, y),
                density = density
            )
        }
    }
}

@Composable
private fun RingAvatar(
    visitor: VisitorRandom,
    config: RingConfig,
    offset: Offset,
    density: Density,
    factor: Float = 0f,
    modifier: Modifier = Modifier,
    hazeModifier: Modifier = Modifier,
) {
    val offsetX = with(density) { offset.x.toDp() }
    val offsetY = with(density) { offset.y.toDp() }

    // 浮动动画 - 每个头像有不同的动画延迟
    val infiniteTransition = rememberInfiniteTransition(label = "avatar_float_${visitor.hashCode()}")

    // 缩放动画
    val scaleAnimation by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = config.avatarScale,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = config.scaleDuration,
                easing = FastOutSlowInEasing
            ),
            repeatMode = RepeatMode.Reverse,
            initialStartOffset = StartOffset(
                offsetMillis = (config.scaleDuration * factor).toInt(),
                offsetType = StartOffsetType.FastForward
            )
        ),
        label = "scale_animation"
    )

    // 旋转动画，以抵消box旋转的问题
    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = config.rotationDuration,
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation_angle"
    )

    AppAvatar(
        online = visitor.isOnline,
        nearby = visitor.isNearby,
        modifier = modifier
            .size(config.avatarSize)
            .offset(offsetX, offsetY)
            .then(if (config.enableRotation)
                Modifier.rotate(- rotationAngle)
            else
                Modifier
            )
            .then(if (config.enableScale)
                Modifier.graphicsLayer {
                    scaleX = scaleAnimation
                    scaleY = scaleAnimation
                }
            else
                Modifier
            )
    ) {
        AsyncImage(
            model = visitor.avatar,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .clip(CircleShape)
                .then(hazeModifier)
                .fillMaxSize()
                .border(
                    width = 1.dp,
                    color = Color.White,
                    shape = CircleShape
                )
        )
    }
}

@Composable
private fun RingStar(
    config: RingStarConfig,
    offset: Offset,
    density: Density,
    modifier: Modifier = Modifier,
) {
    val offsetX = with(density) { offset.x.toDp() }
    val offsetY = with(density) { offset.y.toDp() }

    Box(
        modifier = modifier
            .size(config.size)
            .offset(
                x = offsetX,
                y = offsetY
            ),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .size(config.size)
                .background(config.color, CircleShape)
        )
    }
}

@Preview
@Composable
fun StarRingPreview() {
    val avatars = listOf(
        VisitorRandom("https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"),
        VisitorRandom("https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"),
        VisitorRandom("https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"),
        VisitorRandom(
            "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            isNearby = true,
            isOnline = true
        ),
        VisitorRandom(
            "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            isNearby = true,
            isOnline = false
        ),
        VisitorRandom(
            "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            isNearby = false,
            isOnline = true
        )
    )

    AppTheme {
        StarRing(
            ringAvatars = avatars
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_app_logo_round),
                contentDescription = null,
                modifier = Modifier
                    .clip(CircleShape)
                    .size(64.dp)
            )
        }
    }
}