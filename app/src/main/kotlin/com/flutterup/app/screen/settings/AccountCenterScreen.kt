@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppLineOptionText
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.settings.state.AccountCenterOption

@Composable
fun AccountCenterScreen() {
    val navController = LocalNavController.current

    AccountCenterContent(
        onBackClick = navController::popBackStack,
        onOptionClick = {
            when(it) {
                AccountCenterOption.CHANGE_PWD -> {
                    navController.navigate(ChangePwdRoute)
                }
                AccountCenterOption.DELETE_ACCOUNT -> {
                    navController.navigate(DeleteAccountRoute)
                }
            }
        }
    )
}

@Composable
private fun AccountCenterContent(
    onBackClick: () -> Unit = {},
    onOptionClick: (AccountCenterOption) -> Unit = {}
) {
    val options = remember { AccountCenterOption.entries }

    AppScaffold(
        title = { AppTitleText(stringResource(R.string.account)) },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier.fillMaxSize()
    ) { paddingValues ->
        Box(Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth().align(Alignment.TopCenter)
            )


            LazyColumn(
                modifier = Modifier
                    .padding(paddingValues)
                    .padding(top = 15.dp)
                    .fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 15.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                items(items = options, key = { it.ordinal }) {
                    AppLineOptionText(
                        title = stringResource(it.titleRes),
                        iconDrawableRes = it.iconRes,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .clickable(
                                onClick = { onOptionClick(it) },
                                role = Role.Button
                            )
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun AccountCenterScreenPreview() {
    AppTheme {
        AccountCenterContent()
    }
}