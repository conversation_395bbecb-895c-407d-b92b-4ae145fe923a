package com.flutterup.app.design.component.cardstack

import androidx.compose.animation.core.Animatable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.sqrt

@Composable
fun rememberCardStackState(): CardStackState {
    val scope = rememberCoroutineScope()
    val screenWidth: Float =
        with(LocalDensity.current) { LocalConfiguration.current.screenWidthDp.dp.toPx() }
    val screenHeight: Float =
        with(LocalDensity.current) { LocalConfiguration.current.screenHeightDp.dp.toPx() }
    return remember {
        CardStackState(
            scope = scope,
            screenWidth = screenWidth,
            screenHeight = screenHeight
        )
    }
}

class CardStackState(
    private val scope: CoroutineScope,
    private val screenWidth: Float,
    private val screenHeight: Float,
) {
    private var direction: Direction = Direction.None
    private var visibleCount: Int = 0
    private var stackElevationPx: Float = 0.0f
    private var scaleInterval: Float = 0.0f

    private var alphaInterval: Float = 0f
    private var displacementThresholdPx: Float = 0.0f
    private var animationDuration: Duration = Duration.Normal
    private var rotationMaxDegree: Int = 0
    private var swipeDirection: SwipeDirection = SwipeDirection.Freedom
    private var swipeMethod: SwipeMethod = SwipeMethod.All
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f

    private var onDrag: (Direction, Float) -> Unit = { _, _ ->}

    private var onSwiped: (Direction, Int) -> Unit = { _, _ -> }

    var cardQueue by mutableStateOf<List<CardSate>>(emptyList())
        private set

    var currentCardPage by mutableIntStateOf(0)
        private set

    var size by mutableIntStateOf(0)
        private set

    private var totalItemsCount: Int = 0


    fun init(
        direction: Direction,
        visibleCount: Int,
        stackElevationPx: Float,
        scaleInterval: Float,
        alphaInterval: Float,
        displacementThresholdPx: Float,
        animationDuration: Duration,
        rotationMaxDegree: Int,
        swipeDirection: SwipeDirection,
        swipeMethod: SwipeMethod,
        onDrag: (Direction, Float) -> Unit,
        onSwiped: (Direction, Int) -> Unit
    ) {
        this.direction = direction
        this.visibleCount = visibleCount
        this.stackElevationPx = stackElevationPx
        this.scaleInterval = scaleInterval
        this.alphaInterval = alphaInterval
        this.displacementThresholdPx = displacementThresholdPx
        this.animationDuration = animationDuration
        this.rotationMaxDegree = rotationMaxDegree
        this.swipeDirection = swipeDirection
        this.swipeMethod = swipeMethod
        this.onDrag = onDrag
        this.onSwiped = onSwiped
    }


    private fun getSwipeDirection(): Direction {
        val isOffsetXNegative = dragOffsetX < 0f  //true if user dragged to left
        val isOffsetYNegative = dragOffsetY < 0f  //true if user dragged to upward
        val xThreshHoldReached = abs(dragOffsetX) > displacementThresholdPx
        val yThreshHoldReached = abs(dragOffsetY) > displacementThresholdPx
        return when {
            xThreshHoldReached && yThreshHoldReached -> {
                when {
                    isOffsetXNegative && isOffsetYNegative -> Direction.TopAndLeft
                    isOffsetXNegative -> Direction.BottomAndLeft
                    isOffsetYNegative -> Direction.TopAndRight
                    else -> Direction.BottomAndRight
                }
            }

            xThreshHoldReached -> if (isOffsetXNegative) Direction.Left else Direction.Right
            yThreshHoldReached -> if (isOffsetYNegative) Direction.Top else Direction.Bottom
            else -> Direction.None
        }
    }

    private fun getDragProgress(): Float {
        val xProgress = abs(dragOffsetX) / displacementThresholdPx
        val yProgress = abs(dragOffsetY) / displacementThresholdPx
        return when (swipeDirection) {
            SwipeDirection.Freedom -> sqrt(xProgress.pow(2) + yProgress.pow(2))
            SwipeDirection.Horizontal -> xProgress
            SwipeDirection.Vertical -> yProgress
        }.coerceAtMost(1f)
    }

    private fun getDragDirection(): Direction {
        val isOffsetXNegative = dragOffsetX < 0f  //true if user dragged to left
        val isOffsetYNegative = dragOffsetY < 0f  //true if user dragged to upward
        return when (swipeDirection) {
            SwipeDirection.Freedom -> {
                when {
                    isOffsetXNegative && isOffsetYNegative -> Direction.TopAndLeft
                    isOffsetXNegative -> Direction.BottomAndLeft
                    isOffsetYNegative -> Direction.TopAndRight
                    else -> Direction.BottomAndRight
                }
            }

            SwipeDirection.Horizontal -> if (isOffsetXNegative) Direction.Left else Direction.Right
            SwipeDirection.Vertical -> if (isOffsetYNegative) Direction.Top else Direction.Bottom
        }
    }

    fun onDrag(change: PointerInputChange, dragAmount: Offset) = with(scope) {
        val cardState = cardQueue.firstOrNull() ?: return@with

        if (swipeMethod == SwipeMethod.Automatic || swipeMethod == SwipeMethod.All) {
            when (swipeDirection) {
                SwipeDirection.Freedom -> {
                    dragOffsetX += dragAmount.x
                    dragOffsetY += dragAmount.y
                }

                SwipeDirection.Horizontal -> dragOffsetX += dragAmount.x
                SwipeDirection.Vertical -> dragOffsetY += dragAmount.y
            }
            cardState.snapToTranslation(Offset(dragOffsetX, dragOffsetY))
            val rotationZ = calculateRotation()
            cardState.snapToRotation(rotationZ)

            onDrag(getDragDirection(), getDragProgress())
        }
        change.consume()
    }

    private fun calculateRotation(): Float {
        val resultantOffset = sqrt(dragOffsetX.pow(2) + dragOffsetY.pow(2))
        val calculatedRotationZ = (rotationMaxDegree * resultantOffset) / displacementThresholdPx
        val finalRotationZ = calculatedRotationZ.coerceAtMost(rotationMaxDegree.toFloat())
        return if (dragOffsetX < 0 || dragOffsetY < 0) finalRotationZ.unaryMinus() else finalRotationZ
    }


    fun onDragEnd() {
        val swipeDirection: Direction = getSwipeDirection()
        if (swipeMethod.isAutomaticSwipeAllowed()) {
            swipeInternal(swipeDirection)
        }
    }

    fun <T> initCardQueue(items: List<T>) {
        totalItemsCount = items.size
        currentCardPage = 0
        size = minOf(visibleCount, totalItemsCount)

        cardQueue = List(size) { index ->
            val (tx, ty) = Transformations.calculateTranslation(
                index = index,
                direction = direction,
                visibleCount = visibleCount,
                stackElevationPx = stackElevationPx,
            )
            val (sx, sy) = Transformations.calculateScale(
                index = index,
                visibleCount = visibleCount,
                direction = direction,
                scaleInterval = scaleInterval,
            )

            val alpha = Transformations.calculateAlpha(
                index = index,
                alphaInterval = alphaInterval
            )
            CardSate(
                screenWidth = screenWidth,
                screenHeight = screenHeight,
                scope = scope,
                offsetX = Animatable(tx),
                offsetY = Animatable(ty),
                scaleX = Animatable(sx),
                scaleY = Animatable(sy),
                rotation = Animatable(0.0f),
                zIndex = visibleCount.toFloat() - index,
                alpha = Animatable(alpha),
                animationDuration = animationDuration
            )
        }
    }

    fun swipe(swipeDirection: Direction) {
        if (swipeMethod.isManualSwipeAllowed()) {
            swipeInternal(swipeDirection)
        }
    }

    private fun swipeInternal(swipeDirection: Direction) = scope.launch {
        val cardState = cardQueue.firstOrNull() ?: return@launch
        if (cardState.isAnimating()) return@launch

        cardState.swipeToward(swipeDirection)

        if (swipeDirection != Direction.None) {
            // 重新计算剩余卡片的位置和样式
            // 通知外部当前卡片被滑走
            onSwiped(swipeDirection, currentCardPage)

            // 更新start和end索引
            currentCardPage = (currentCardPage + 1).coerceAtMost(totalItemsCount - 1)

            cardQueue.forEachIndexed { index, cardSate ->
                val translateOffset = Transformations.calculateTranslation(
                    index = index,
                    direction = direction,
                    visibleCount = visibleCount,
                    stackElevationPx = stackElevationPx,
                )
                val scaleOffset = Transformations.calculateScale(
                    index = index,
                    visibleCount = visibleCount,
                    direction = direction,
                    scaleInterval = scaleInterval,
                )
                val alpha = Transformations.calculateAlpha(
                    index = index,
                    alphaInterval = alphaInterval
                )
                cardQueue[index].run {
                    translateTo(translateOffset)
                    scaleTo(scaleOffset)
                    alphaTo(alpha)
                }
            }
        }
        dragOffsetX = 0f
        dragOffsetY = 0f
    }

    /**
     * 检查是否还有更多卡片可以显示
     */
    fun hasMoreCards(): Boolean {
        return currentCardPage < totalItemsCount
    }
}