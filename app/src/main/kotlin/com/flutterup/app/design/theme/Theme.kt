package com.flutterup.app.design.theme

import androidx.annotation.VisibleForTesting
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.valentinilk.shimmer.LocalShimmerTheme
import com.valentinilk.shimmer.ShimmerTheme
import com.valentinilk.shimmer.defaultShimmerTheme
import com.valentinilk.shimmer.shimmerSpec

/**
 * Light default theme color scheme
 */
@VisibleForTesting
val DefaultColorScheme = lightColorScheme(
    primary = Color.White,
    onPrimary = Color.White,
    primaryContainer = PurpleSecondary,
    onPrimaryContainer = Color.White,
    secondary = Color.White,
    onSecondary = Color.White,
    secondaryContainer = Color.White,
    onSecondaryContainer = Color.White,
    tertiary = Color.White,
    onTertiary = Color.White,
    tertiaryContainer = PurpleTertiaryContainer,
    onTertiaryContainer = Color.White,
    error = ErrorPrimary,
    onError = ErrorPrimary,
    errorContainer = Color.White,
    onErrorContainer = Color.White,
    background = PinkPrimary,
    onBackground = Color.White,
    surface = PinkPrimary,
    onSurface = Color.White,
    surfaceVariant = Color.White,
    onSurfaceVariant = Color.White,
    inverseSurface = Color.White,
    inverseOnSurface = Color.White,
    outline = Color.White,
)

val ColorScheme.DefaultBackgroundTheme get() = BackgroundTheme(
    color = surface,
    tonalElevation = 2.dp,
)

val DefaultShimmerTheme get() = defaultShimmerTheme.copy(
    animationSpec = infiniteRepeatable(
        animation = shimmerSpec(
            durationMillis = 800,
            easing = LinearEasing,
            delayMillis = 200,
        ),
        repeatMode = RepeatMode.Restart,
    ),
)

/**
 * Now in Android theme.
 */
@Composable
fun AppTheme(
    colorScheme: ColorScheme = DefaultColorScheme,
    backgroundTheme: BackgroundTheme = colorScheme.DefaultBackgroundTheme,
    shimmerTheme: ShimmerTheme = DefaultShimmerTheme,
    content: @Composable () -> Unit,
) {
    // Composition locals
    CompositionLocalProvider(
        LocalBackgroundTheme provides backgroundTheme,
        LocalShimmerTheme provides shimmerTheme
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = FlutterUpTypography,
            content = content,
        )
    }
}

