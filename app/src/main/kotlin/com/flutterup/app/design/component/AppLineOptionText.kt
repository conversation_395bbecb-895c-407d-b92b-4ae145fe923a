package com.flutterup.app.design.component

import android.content.res.Resources
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack666


@Composable
fun AppLineOptionText(
    title: String,
    modifier: Modifier = Modifier,
    isProgressing: Boolean = false,
    trackColor: Color = PurplePrimary,
    background: Color = Color.White,
    shape: RoundedCornerShape = RoundedCornerShape(16.dp),
    @DrawableRes iconDrawableRes: Int? = null,
    @DrawableRes rightArrowDrawableRes: Int? = R.drawable.ic_right_purple_arrow,
    iconBackgroundEnable: Boolean = true,
    textColor: Color = TextBlack666,
) {
    Row(
        modifier = modifier.background(background, shape),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (isProgressing) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(20.dp)
                    ,
                    strokeWidth = 2.dp,
                    trackColor = trackColor
                )
            }
        } else {
            Spacer(modifier = Modifier.width(20.dp))

            if (iconDrawableRes != null && iconDrawableRes != Resources.ID_NULL) {

                if (iconBackgroundEnable) {
                    Box(
                        modifier = Modifier
                            .size(26.dp)
                            .background(Color(0xFFEDDDFD), shape = RoundedCornerShape(9.dp)),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(iconDrawableRes),
                            contentDescription = null
                        )
                    }
                } else {
                    Image(
                        painter = painterResource(iconDrawableRes),
                        contentDescription = null
                    )
                }

                Spacer(modifier = Modifier.width(6.dp))
            }

            Text(
                text = title,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    color = textColor,
                ),
                modifier = Modifier.weight(1f)
            )

            if (rightArrowDrawableRes != null && rightArrowDrawableRes != Resources.ID_NULL) {
                Image(
                    painter = painterResource(rightArrowDrawableRes),
                    contentDescription = null,
                )

                Spacer(Modifier.width(20.dp))
            }
        }
    }
}


@Preview
@Composable
private fun SettingsOptionPreview(
    @PreviewParameter(BooleanProvider::class) isProgressing: Boolean,
) {
    AppTheme {
        AppLineOptionText(
            title = "Account",
            isProgressing = isProgressing,
            iconDrawableRes = R.drawable.ic_settings_account,
            modifier = Modifier.fillMaxWidth().height(50.dp)
        )
    }
}