package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LabelBackgroundPrimary


@Composable
fun AppNearby(
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(3.dp),
    horizontalArrangement: Arrangement.Horizontal = Arrangement.spacedBy(1.dp),
    backgroundColor: Color = LabelBackgroundPrimary,
    shape: Shape = RoundedCornerShape(20.dp)
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = horizontalArrangement,
        modifier = modifier.background(
            color = backgroundColor,
            shape = shape
        ),
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_nearyby_nonoutline),
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier
                .padding(
                    start = contentPadding.calculateStartPadding(LayoutDirection.Ltr),
                    top = contentPadding.calculateTopPadding(),
                    bottom = contentPadding.calculateBottomPadding()
                )
        )

        Text(
            text = stringResource(R.string.nearby),
            style = TextStyle(
                fontSize = 10.sp,
                fontWeight = FontWeight.W400,
                color = Color.White,
            ),
            modifier = Modifier.padding(
                end = contentPadding.calculateEndPadding(LayoutDirection.Ltr),
                top = contentPadding.calculateTopPadding(),
                bottom = contentPadding.calculateBottomPadding()
            )
        )
    }
}

@Preview
@Composable
private fun AppNearbyPreview() {
    AppTheme {
        AppNearby()
    }
}