package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.minimumInteractiveComponentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleSelectable
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme

@Composable
fun AppRadioButton(
    selected: Boolean,
    modifier: Modifier = Modifier,
    tint: Color = Color.Unspecified,
    onSelectedChange: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .minimumInteractiveComponentSize()
                .wrapContentSize()
                .clip(CircleShape)
                .background(color = Color.Transparent)
                .noRippleSelectable(
                    selected = selected,
                    role = Role.RadioButton,
                    onClick = onSelectedChange
                )
    ) {
        Icon(
            painter = painterResource(id = if (selected) R.drawable.ic_radio_button_selected else R.drawable.ic_radio_button_unselected),
            contentDescription = null,
            tint = tint,
            modifier = Modifier.size(14.dp)
        )
    }
}


@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun AppRadioButtonPreview(
    @PreviewParameter(BooleanProvider::class) checked: Boolean
) {
    AppTheme {
        AppRadioButton(
            selected = checked,
            onSelectedChange = {},
        )
    }
}