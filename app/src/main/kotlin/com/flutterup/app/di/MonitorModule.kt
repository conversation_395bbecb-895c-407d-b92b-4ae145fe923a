package com.flutterup.app.di

import com.flutterup.app.utils.AppStatusMonitor
import com.flutterup.app.utils.AppStatusMonitorImpl
import com.flutterup.app.utils.UserUnreadMonitorImpl
import com.flutterup.app.utils.UserUnreadMonitor
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent


@Module
@InstallIn(SingletonComponent::class)
abstract class MonitorModule {

    @Binds
    abstract fun bindUserUnreadMonitor(monitor: UserUnreadMonitorImpl): UserUnreadMonitor

    @Binds
    abstract fun bindAppStatusMonitor(monitor: AppStatusMonitorImpl): AppStatusMonitor
}