<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="FlutterUp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>



    <style name="FlutterUp.Splash" parent="Theme.SplashScreen">
        <item name="android:windowBackground">@color/purple_color_primary</item>

        <item name="android:windowSplashScreenBackground" tools:targetApi="31">@color/purple_color_primary</item>
        <item name="android:windowSplashScreenAnimatedIcon" tools:targetApi="31">@mipmap/ic_app_logo</item>

        <item name="postSplashScreenTheme">@style/FlutterUp</item>

        <!--   设置全屏  -->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <!--   设置状态栏亮色模式   -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
</resources>