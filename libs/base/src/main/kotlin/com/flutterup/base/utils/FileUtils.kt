package com.flutterup.base.utils

import android.content.Context
import android.net.Uri
import java.io.File

object FileUtils {
    const val IMAGE_SUFFIX = ".png"
    const val VIDEO_SUFFIX = ".mp4"

    const val CACHE_PARENT_DIR = "app_cache"

    fun uriToCacheFile(
        context: Context,
        uri: Uri,
        suffix: String,
        fileName: String = "temp_${System.currentTimeMillis()}",
    ): File {
        val inputStream = context.contentResolver.openInputStream(uri)
        val cacheFile = generateFile(context, suffix, fileName)
        cacheFile.outputStream().use { outputStream ->
            inputStream?.copyTo(outputStream)
        }
        return cacheFile
    }

    fun generateFile(
        context: Context,
        suffix: String,
        fileName: String = "temp_${System.currentTimeMillis()}",
    ): File {
        return File(context.cacheDir, "$fileName$suffix")
    }

    fun generateFileName(suffix: String): String {
        return "temp_${System.currentTimeMillis()}$suffix"
    }
}