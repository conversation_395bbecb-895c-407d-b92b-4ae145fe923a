package com.flutterup.base.utils

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.annotation.UiThread
import com.flutterup.base.BaseApplication
import com.flutterup.base.BuildConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object Timber {
    private const val TAG = "Timber"

    /**
     * Toast 管理相关变量
     */
    private var toast: Toast? = null
    
    private var lastToastMessage: String? = null
    
    private var isToastShowing = false
    

    fun showToast(
        content: String?,
        duration: Int = Toast.LENGTH_LONG
    ) {
        if (content == null) return

        // 如果toast正在显示，并且上次显示的内容相同，则不重复显示
        if (isToastShowing && content == lastToastMessage) {
            return
        }
        // 更新最后显示的文本
        lastToastMessage = content
        
        if (isMainThread()) {
            showToastOnMainThread(content, duration)
        } else {
            BaseApplication.getApplicationScope().launch(Dispatchers.Main) {
                showToastOnMainThread(content, duration)
            }
        }
    }

    @UiThread
    private fun showToastOnMainThread(content: String?, duration: Int = Toast.LENGTH_LONG) {
        // 取消当前显示的Toast
        toast?.cancel()
        toast = Toast.makeText(BaseApplication.getApplicationContext(), content, duration)
        toast?.show()

        isToastShowing = true
        // Toast显示完成后自动重置状态
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed(
            {
                isToastShowing = false
            },
            if (duration == Toast.LENGTH_LONG) 3500L else 2000L // LENGTH_LONG约3.5秒，LENGTH_SHORT约2秒
        )
    }


    /**
     * Logs an error message. The last argument can be a Throwable.
     * Example: Timber.e("MyTag", "Something went wrong", exception)
     */
    fun e(key: String? = TAG, vararg messages: Any?) = logInternal(key, Log.ERROR, *messages)

    /**
     * Logs an info message. The last argument can be a Throwable.
     * Example: Timber.i("MyTag", "Operation successful", details) // details is not a Throwable
     * Example: Timber.i("MyTag", "Operation finished", exception) // exception is a Throwable
     */
    fun i(key: String? = TAG, vararg messages: Any?) = logInternal(key, Log.INFO, *messages)

    /**
     * Logs a warning message. The last argument can be a Throwable.
     * Example: Timber.w("MyTag", "Potential issue detected", warningDetails)
     * Example: Timber.w("MyTag", "Deprecated method used", exception)
     */
    fun w(key: String? = TAG, vararg messages: Any?) = logInternal(key, Log.WARN, *messages)

    /**
     * Logs a debug message. The last argument can be a Throwable.
     * Example: Timber.d("MyTag", "Debugging information", debugDetails)
     * Example: Timber.d("MyTag", "Debugging finished", exception)
     */
    fun d(key: String? = TAG, vararg messages: Any?) = logInternal(key, Log.DEBUG, *messages)

    private fun logInternal(key: String?, level: Int, vararg messages: Any?) {
        // Only log in debug builds
        if (!BuildConfig.DEBUG) return

        // If no messages are provided, do nothing
        if (messages.isEmpty()) {
            return
        }

        // Check if the last argument is a Throwable
        val potentialThrowable = messages.lastOrNull()
        val throwable: Throwable? = potentialThrowable as? Throwable

        // Separate the message parts from the potential Throwable
        val messageParts = if (throwable != null) messages.dropLast(1) else messages.toList()

        // Construct the log message string
        // If only a throwable was passed, use its message or class name as the log text
        val text = if (messageParts.isEmpty()) {
            throwable?.message ?: throwable?.javaClass?.simpleName ?: "" // Handle case where only throwable is passed
        } else {
            // Join the remaining parts into a single string, handling nulls
            messageParts.joinToString { it?.toString() ?: "null" }
        }

        // Perform the actual logging based on the level
        when (level) {
            Log.ERROR -> Log.e(key, text, throwable)
            Log.INFO -> Log.i(key, text, throwable)
            Log.WARN -> Log.w(key, text, throwable)
            Log.DEBUG -> Log.d(key, text, throwable)
            Log.VERBOSE -> Log.v(key, text, throwable)
        }
    }

    private fun isMainThread(): Boolean {
        return Looper.myLooper() == Looper.getMainLooper()
    }
}