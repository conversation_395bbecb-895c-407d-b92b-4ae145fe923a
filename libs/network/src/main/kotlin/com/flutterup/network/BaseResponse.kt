package com.flutterup.network

import android.os.Parcelable
import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Keep
@JsonClass(generateAdapter = true)
data class BaseResponse<T : Any>(
    @Json(name = "code")
    val code: Int,

    @Json(name = "msg")
    val message: String? = null,

    @Json(name = "data")
    val data: T? = null,

    @Json(name = "action")
    val action: Action? = null
) {
    val isSuccess: Boolean get() = code == 0
}


@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class Action(
    @Json(name = "id")
    val id: Int? = null,

    @Json(name = "param")
    val params: @RawValue Any? = null,

    @<PERSON><PERSON>(name = "trace_id")
    val traceId: Long? = null,
) : Parcelable